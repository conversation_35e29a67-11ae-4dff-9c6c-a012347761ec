"""Main API for Brand Event Agent system."""

import uuid
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Literal
from pydantic import BaseModel, Field
from langchain_core.messages import HumanMessage

from src.utils.logger import get_workflow_logger,LogMessages
from src.core.workflow import BrandEventWorkflow
from src.core.state import WorkflowState, WorkflowStatus, create_initial_state
from src.messages.event_handler import event_handler

# ==================== API Schemas ====================

class ServiceToken(BaseModel):
    """Service token schema for IdaaS integration."""
    service_id: str = Field(..., description="服务ID（接入IdaaS）")
    access_token: str = Field(..., description="服务access_token")


class Extensions(BaseModel):
    """Extensions schema for additional features."""
    tokens: Optional[List[ServiceToken]] = Field(None, description="服务token列表")
    additional_fields: Dict[str, Any] = Field(default_factory=dict, description="其他扩展字段")


class ChatRequest(BaseModel):
    """Request schema for chat endpoint."""
    message: str = Field(..., description="用户发送的消息内容")
    session_id: Optional[str] = Field(None, description="会话ID")
    task_id: Optional[str] = Field(None, description="任务ID")
    sandbox_id: Optional[str] = Field(None, description="沙箱ID")
    event_webhook: Optional[str] = Field(None, description="上传事件回调地址")
    extensions: Optional[Extensions] = Field(None, description="扩展字段")

    # 报表DSL字段
    report_dsl_data: Optional[Dict[str, Any]] = Field(None, description="报表DSL数据结构，用于生成报告")
    report_dsl_status: Optional[Literal["SUCCESS", "FAILED"]] = Field(None, description="报表DSL生成状态")
    report_dsl_message: Optional[str] = Field(None, description="报表DSL状态消息，FAILED时包含错误信息")

    # 工作流状态字段
    workflow_status: Optional[WorkflowStatus] = Field(None, description="期望的工作流状态，用于动态控制流程")

    # 保留原有字段以保持向后兼容
    user_id: Optional[str] = Field(None, description="用户ID（向后兼容）")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据（向后兼容）")

class ChatResponse(BaseModel):
    """Response schema for chat endpoint."""
    session_id: str = Field(..., description="会话ID")
    response: str = Field(..., description="系统响应")
    status: str = Field(..., description="当前工作流状态")
    requires_feedback: bool = Field(False, description="是否需要用户反馈")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="响应元数据")

    # 新增字段
    task_id: Optional[str] = Field(None, description="任务ID")
    sandbox_id: Optional[str] = Field(None, description="沙箱ID")
    event_webhook: Optional[str] = Field(None, description="事件回调地址")
    extensions: Optional[Dict[str, Any]] = Field(None, description="扩展字段")


class SessionInfo(BaseModel):
    """Session information schema."""
    session_id: str
    user_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    status: str
    message_count: int = 0
    metadata: Dict[str, Any] = Field(default_factory=dict)


class SystemStatus(BaseModel):
    """System status schema."""
    status: str = Field(..., description="System status")
    version: str = Field(..., description="System version")
    uptime: str = Field(..., description="System uptime")
    active_sessions: int = Field(..., description="Number of active sessions")
    total_sessions: int = Field(..., description="Total sessions created")
    tools_available: int = Field(..., description="Number of available tools")
    agents_active: int = Field(..., description="Number of active agents")


class HealthCheck(BaseModel):
    """Health check response schema."""
    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.now)
    services: Dict[str, str] = Field(default_factory=dict)
    version: str = "1.0.0"
    tools_count: int = 0
    agents_count: int = 0

# ==================== Main API Class ====================

class BrandEventAPI:
    """Main API class for Brand Event Agent system."""

    def __init__(self, checkpointer=None):
        
        self.logger = get_workflow_logger("API")
        self.start_time = datetime.now()

        # Initialize workflow
        self.workflow = BrandEventWorkflow(checkpointer=checkpointer)

        # Session tracking
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.total_sessions = 0

        #
        self.create_initial_state = create_initial_state
        self.event_handler = event_handler

        self.logger.info("Brand Event API initialized successfully")


    async def chat(self, request: ChatRequest) -> ChatResponse:
        """Handle chat request."""
        try:
            # Generate or use existing session ID
            session_id = request.session_id or str(uuid.uuid4())

            # Print request information
            print("=" * 50)
            print("REQUEST INFORMATION:")
            print("=" * 50)
            print(f"Message: {request.message}")
            print(f"Session ID: {request.session_id}")
            print(f"Task ID: {request.task_id}")
            print(f"Sandbox ID: {request.sandbox_id}")
            print(f"Event Webhook: {request.event_webhook}")
            print(f"User ID: {request.user_id}")
            print(f"Workflow Status: {request.workflow_status}")
            print(f"Report DSL Data: {request.report_dsl_data}")
            print(f"Report DSL Status: {request.report_dsl_status}")
            print(f"Report DSL Message: {request.report_dsl_message}")
            print(f"Extensions: {request.extensions}")
            print(f"Metadata: {request.metadata}")
            print("=" * 50)

            self.logger.info(LogMessages.CHAT_REQUEST_START, session_id=session_id)

            # Check if this is a new session
            if session_id not in self.active_sessions:
                # Create new session
                self.active_sessions[session_id] = {
                    "created_at": datetime.now(),
                    "user_id": request.user_id,
                    "task_id": request.task_id,
                    "sandbox_id": request.sandbox_id,
                    "event_webhook": request.event_webhook,
                    "extensions": request.extensions.dict() if request.extensions else None,
                    "message_count": 1,
                    "workflow_status": request.workflow_status,
                }
                self.total_sessions += 1

                # Create initial state
                initial_state = self.create_initial_state(
                    session_id=session_id,
                    user_input=request.message,
                    user_id=request.user_id,
                    task_id=request.task_id,
                    sandbox_id=request.sandbox_id,
                    event_webhook=request.event_webhook,
                    extensions=request.extensions,
                    report_dsl_data=request.report_dsl_data,
                    report_dsl_status=request.report_dsl_status,
                    report_dsl_message=request.report_dsl_message,
                    workflow_status=request.workflow_status
                )

                # Run workflow with streaming
                config = {"configurable": {"thread_id": session_id}}
                final_state = await self._process_workflow_stream(initial_state, config, initial_state)

            else:
                # Continue existing session
                self.active_sessions[session_id]["message_count"] += 1

                # Update session with new request data if provided
                if request.workflow_status:
                    self.active_sessions[session_id]["workflow_status"] = request.workflow_status

                # Get current state and update it with new data
                config = {"configurable": {"thread_id": session_id}}
                current_state = self.workflow.workflow.get_state(config)
                state_for_events = current_state.values if current_state else {}

                # Add user message to continue conversation
                user_message = {"messages": [HumanMessage(content=request.message)]}

                # Update state with new report_dsl_data and workflow_status if provided
                if request.report_dsl_data or request.report_dsl_status or request.report_dsl_message or request.workflow_status:
                    update_data = {}
                    if request.report_dsl_data:
                        update_data["report_dsl_data"] = request.report_dsl_data
                    if request.report_dsl_status:
                        update_data["report_dsl_status"] = request.report_dsl_status
                    if request.report_dsl_message:
                        update_data["report_dsl_message"] = request.report_dsl_message
                    if request.workflow_status:
                        update_data["workflow_status"] = request.workflow_status

                    # Merge user message with state updates
                    user_message.update(update_data)

                # Continue workflow with streaming
                final_state = await self._process_workflow_stream(user_message, config, state_for_events)

            # Generate response from final state
            complete_state = final_state.values if final_state else {}
            response = self._generate_response(complete_state, session_id, request)

            self.logger.info(LogMessages.CHAT_REQUEST_COMPLETE, session_id=session_id)
            return response

        except Exception as e:
            self.logger.error(f"Error processing chat request: {e}", session_id=session_id, exc_info=True)

            error_response = ChatResponse(
                session_id=request.session_id or "error",
                response=f"抱歉，处理您的请求时发生了错误：{str(e)}",
                status="error",
                requires_feedback=False,
                task_id=request.task_id,
                sandbox_id=request.sandbox_id,
                event_webhook=request.event_webhook,
                extensions=request.extensions.dict() if request.extensions else None,
                metadata={"error": str(e)}
            )

            return error_response

    async def _process_workflow_stream(self, input_data, config, state_for_events):
        """处理工作流流式消息的公共方法"""
        final_state = None
        async for chunk in self.workflow.workflow.astream(input_data, config=config, stream_mode="custom"):
            if "live_status_message" in chunk:
                # 显示实时状态
                self.event_handler.send_live_status(chunk['live_status_message'], state=state_for_events)
            elif "agent_message" in chunk:
                # 显示Agent消息
                self.event_handler.send_agent_message(state_for_events, chunk['agent_message'])
            elif "agent_message_with_file" in chunk:
                # 显示Agent消息
                self.event_handler.send_agent_message(
                    state_for_events,
                    chunk['agent_message_with_file']["content"],
                    attachments=chunk['agent_message_with_file']['attachments']
                )
            elif "human_feedback_message" in chunk:
                # 显示人工审批状态
                self.event_handler.send_agent_status(
                    status="idle",
                    brief="等待用户确认",
                    description=chunk['human_feedback_message'],
                    state=state_for_events
                )
            elif "agent_status_waiting" in chunk:
                # 显示等待状态
                self.event_handler.send_agent_status(
                    status="waiting",
                    brief="等待用户确认",
                    description=chunk['agent_status_waiting'],
                    state=state_for_events
                )

        # 获取最终状态
        return self.workflow.workflow.get_state(config)



    def _generate_response(self, state: Dict[str, Any], session_id: str, request: ChatRequest) -> ChatResponse:
        """Generate API response from workflow state."""
        try:
            # Determine response message and status
            workflow_status = state.get("workflow_status", WorkflowStatus.INITIALIZING)

            if workflow_status == WorkflowStatus.INITIALIZING:
                # Get the last AI message
                messages = state.get("messages", [])
                response_message = "您好！我是品牌舆情分析助手。"
                for msg in reversed(messages):
                    if hasattr(msg, 'content') and hasattr(msg, '__class__') and 'AI' in str(msg.__class__):
                        response_message = msg.content
                        break
                status = "waiting_feedback"
                requires_feedback = True

            elif workflow_status in [WorkflowStatus.CLARIFYING_INTENT, WorkflowStatus.PLANNING]:
                # Get the last AI message
                messages = state.get("messages", [])
                response_message = "请提供更多信息"
                for msg in reversed(messages):
                    if hasattr(msg, 'content') and hasattr(msg, '__class__') and 'AI' in str(msg.__class__):
                        response_message = msg.content
                        break
                status = "waiting_feedback"
                requires_feedback = True

            elif workflow_status in [WorkflowStatus.EXECUTING, WorkflowStatus.REPORT]:
                response_message = "分析正在进行中..."
                status = "processing"
                requires_feedback = False

            elif workflow_status == WorkflowStatus.FAILED:
                error_info = state.get("error_info", {})
                response_message = f"分析失败：{error_info.get('message', '未知错误')}"
                status = "failed"
                requires_feedback = False

            else:
                # Default case
                response_message = state.get("final_report", "分析已完成")
                status = "completed"
                requires_feedback = False

            return ChatResponse(
                session_id=session_id,
                response=response_message,
                status=status,
                requires_feedback=requires_feedback,
                task_id=request.task_id,
                sandbox_id=request.sandbox_id,
                event_webhook=request.event_webhook,
                extensions=request.extensions.dict() if request.extensions else None,
                metadata={
                    "workflow_status": str(workflow_status),
                    "intent_clarified": state.get("intent_clarified", False),
                    "plan_approved": state.get("plan_approved", False),
                    "final_report": state.get("final_report"),
                    "html_report": state.get("html_report")
                }
            )

        except Exception as e:
            self.logger.error(f"Error generating response: {e}", session_id=session_id, exc_info=True)
            return ChatResponse(
                session_id=session_id,
                response=f"生成响应时发生错误：{str(e)}",
                status="error",
                requires_feedback=False,
                metadata={"error": str(e)}
            )

    def get_session_info(self, session_id: str) -> Optional[SessionInfo]:
        """Get session information."""
        if session_id not in self.active_sessions:
            return None

        session_data = self.active_sessions[session_id]
        return SessionInfo(
            session_id=session_id,
            user_id=session_data.get("user_id"),
            created_at=session_data.get("created_at"),
            updated_at=datetime.now(),
            status="active",
            message_count=session_data.get("message_count", 0),
            metadata=session_data
        )

    def get_system_status(self) -> SystemStatus:
        """Get system status information."""
        uptime = datetime.now() - self.start_time

        return SystemStatus(
            status="healthy",
            version="1.0.0",
            uptime=str(uptime),
            active_sessions=len(self.active_sessions),
            total_sessions=self.total_sessions,
            tools_available=0,  # TODO: Get actual tool count
            agents_active=1  # TODO: Get actual agent count
        )

    def health_check(self) -> HealthCheck:
        """Perform health check."""
        return HealthCheck(
            status="healthy",
            services={
                "workflow": "healthy",
                "event_handler": "healthy"
            },
            tools_count=0,  # TODO: Get actual tool count
            agents_count=1  # TODO: Get actual agent count
        )

