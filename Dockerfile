FROM artifactory.ep.chehejia.com/licloud-docker/base/run/python:${sdkVersion}

# 创建应用目录并设置权限
RUN mkdir -p /chj/app && chmod 775 -R /chj

WORKDIR /chj/app

# 部署的源代码路径（默认将全部源代码拷贝到镜像里）
ENV SOURCE_PATH=.

# 安装系统依赖
#RUN apt-get update && apt-get install -y --no-install-recommends \
#    git \
#    && rm -rf /var/lib/apt/lists/*

# 安装 uv 包管理工具
RUN pip install uv

# 先复制依赖文件
COPY ${SOURCE_PATH} .

# 安装依赖（添加 --system 参数在系统 Python 环境中安装）
RUN uv sync

# 复制应用代码
COPY ${SOURCE_PATH} .

# 暴露 API 端口
ENV SERVICE_PORT=8000
EXPOSE ${SERVICE_PORT}

# 使用 uv run 运行服务器，指定正确的模块路径
CMD ["uv", "run", "uvicorn", "src.api.app:app", "--host", "0.0.0.0", "--port", "8000"]