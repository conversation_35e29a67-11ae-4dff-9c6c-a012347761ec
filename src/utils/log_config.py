"""
日志配置管理器

提供统一的日志配置管理，支持文件和控制台双重输出，按日期轮转日志文件。
"""

import os
import logging
from pathlib import Path
from datetime import datetime
from logging.handlers import TimedRotatingFileHandler
from typing import Optional


class LogConfig:
    """
    日志配置管理类
    
    负责统一管理应用的日志配置，包括：
    - 创建日志目录
    - 配置文件和控制台处理器
    - 设置日志格式
    - 支持按日期轮转
    """
    
    def __init__(self, log_dir: str = "/chj/data/log/brand-event-agent/logs", log_level: str = "INFO",
                 log_file_prefix: str = "brand_event"):
        """
        初始化日志配置
        
        Args:
            log_dir: 日志目录路径
            log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            log_file_prefix: 日志文件前缀
        """
        self.log_dir = Path(log_dir)
        self.log_level = getattr(logging, log_level.upper())
        self.log_file_prefix = log_file_prefix
        self.log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        self.date_format = "%Y-%m-%d %H:%M:%S"
        
    def setup_logging(self):
        """
        设置全局日志配置
        
        配置根logger，添加文件和控制台处理器，设置统一的日志格式。
        """
        # 创建日志目录
        self.log_dir.mkdir(exist_ok=True)
        
        # 获取根logger
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # 清除现有handlers，避免重复配置
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 创建格式化器
        formatter = logging.Formatter(
            fmt=self.log_format,
            datefmt=self.date_format
        )
        
        # 配置文件处理器 - 按日期轮转
        log_file = self.log_dir / f"{self.log_file_prefix}_{datetime.now().strftime('%Y-%m-%d')}.log"
        file_handler = TimedRotatingFileHandler(
            filename=str(log_file),
            when='midnight',
            interval=1,
            encoding='utf-8',
            backupCount=0  # 不自动删除旧文件
        )
        file_handler.setLevel(self.log_level)
        file_handler.setFormatter(formatter)
        
        # 设置文件名格式（不包含时间戳后缀）
        file_handler.suffix = ""
        file_handler.namer = self._custom_namer
        
        # 配置控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(formatter)
        
        # 添加处理器到根logger
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
        
        # 记录日志配置完成
        logging.info(f"日志配置完成 - 目录: {self.log_dir}, 级别: {logging.getLevelName(self.log_level)}")
    
    def _custom_namer(self, default_name: str) -> str:
        """
        自定义日志文件命名规则
        
        Args:
            default_name: 默认文件名
            
        Returns:
            自定义的文件名
        """
        # 提取日期部分，保持 brand_event_YYYY-MM-DD.log 格式
        base_name = Path(default_name).stem
        if '.' in base_name:
            date_part = base_name.split('.')[-1]
            return str(self.log_dir / f"{self.log_file_prefix}_{date_part}.log")
        return default_name


# 全局配置状态
_logging_configured = False


def setup_logging(log_dir: Optional[str] = None, 
                  log_level: Optional[str] = None,
                  log_file_prefix: Optional[str] = None) -> None:
    """
    全局日志配置函数
    
    Args:
        log_dir: 日志目录，默认从环境变量LOG_DIR获取，否则使用'./logs'
        log_level: 日志级别，默认从环境变量LOG_LEVEL获取，否则使用'INFO'
        log_file_prefix: 日志文件前缀，默认使用'brand_event'
    """
    global _logging_configured
    
    # 避免重复配置
    if _logging_configured:
        return
    
    # 从环境变量或参数获取配置
    log_dir = log_dir or os.getenv('LOG_DIR', './logs')
    log_level = log_level or os.getenv('LOG_LEVEL', 'INFO')
    log_file_prefix = log_file_prefix or 'brand_event'
    
    # 创建并应用配置
    config = LogConfig(log_dir, log_level, log_file_prefix)
    config.setup_logging()
    
    _logging_configured = True


def ensure_logging_configured() -> None:
    """
    确保日志系统已配置
    
    如果尚未配置，则使用默认配置进行初始化。
    这个函数被WorkflowLogger调用，确保在创建logger时日志系统已正确配置。
    """
    global _logging_configured
    
    if not _logging_configured:
        setup_logging()


def reset_logging_configuration() -> None:
    """
    重置日志配置状态
    
    主要用于测试环境，允许重新配置日志系统。
    """
    global _logging_configured
    _logging_configured = False


def get_log_file_path(log_dir: Optional[str] = None, 
                      log_file_prefix: Optional[str] = None,
                      date: Optional[datetime] = None) -> Path:
    """
    获取指定日期的日志文件路径
    
    Args:
        log_dir: 日志目录
        log_file_prefix: 日志文件前缀
        date: 日期，默认为当前日期
        
    Returns:
        日志文件路径
    """
    log_dir = log_dir or os.getenv('LOG_DIR', '/chj/data/log/brand-event-agent')
    log_file_prefix = log_file_prefix or 'brand_event'
    date = date or datetime.now()
    
    log_dir_path = Path(log_dir)
    date_str = date.strftime('%Y-%m-%d')
    
    return log_dir_path / f"{log_file_prefix}_{date_str}.log"


def is_logging_configured() -> bool:
    """
    检查日志系统是否已配置
    
    Returns:
        True if logging is configured, False otherwise
    """
    return _logging_configured
