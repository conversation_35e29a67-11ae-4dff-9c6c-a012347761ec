2025-08-11 15:47:05 - root - INFO - 日志配置完成 - 目录: logs, 级别: INFO
2025-08-11 15:47:05 - brand_event.api - INFO - [API] Brand Event API initialized successfully
2025-08-11 15:47:05 - brand_event.api - INFO - [API] Brand Event API initialized successfully
2025-08-11 16:11:16 - brand_event.api - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [API] Processing chat request
2025-08-11 16:11:16 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] Start doing workflow supervision
2025-08-11 16:11:19 - httpx - INFO - HTTP Request: POST https://llm-model-proxy.dev.fc.chj.cloud/agentops/chat/completions "HTTP/1.1 200 OK"
2025-08-11 16:11:19 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] User message analysis: type=task
2025-08-11 16:11:19 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] LLM routing decision: report, reason: 用户消息为任务请求，当前工作流状态为REPORT，因此路由到report。
2025-08-11 16:11:19 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] Completed workflow supervision, result: route to report, reason: 用户消息为任务请求，当前工作流状态为REPORT，因此路由到report。
2025-08-11 16:11:19 - agentops_event_sdk_python.subscribers.standard - INFO - Init SyncWebhookSubscriber: https://xuanji-dev.chehejia.com/api/v1/agent-events, timeout: 10s, retries: 3
2025-08-11 16:11:19 - src.messages.event_handler - INFO - 事件管理器已初始化，Webhook URL: https://xuanji-dev.chehejia.com/api/v1/agent-events, 控制台输出: True
2025-08-11 16:11:19 - agentops_event_sdk_python.subscribers.standard - INFO - Event[bddf762c-e4eb-40bc-bdfd-3f9e804e8818] sent in 0.06s
2025-08-11 16:11:19 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在分析当前状态和用户消息...
2025-08-11 16:11:19 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在分析当前状态和用户消息...... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:19 - agentops_event_sdk_python.subscribers.standard - INFO - Event[82b4d779-cee9-49d3-a7e3-9aeea365b1ed] sent in 0.10s
2025-08-11 16:11:19 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在分析用户消息类型和意图...
2025-08-11 16:11:19 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在分析用户消息类型和意图...... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:19 - brand_event.reportnode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [ReportNode] Starting final report generation
2025-08-11 16:11:19 - brand_event.reportnode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [ReportNode] Starting report generation
2025-08-11 16:11:23 - BrandEventTools - INFO - Generating HTML report from DSL data
2025-08-11 16:11:23 - BrandEventTools - INFO - Report generation dsl_data: {'section1': {'type': 'section', 'title': '基本信息', 'description': '', 'content': [{'type': 'descriptions', 'data': [{'id': 1, 'label': '统计时间', 'value': '截止5月16日16:00'}, {'id': 2, 'label': '内容量', 'value': 14069}, {'id': 3, 'label': '口碑指数', 'value': '80.15%'}, {'id': 4, 'label': '正面情感比例', 'value': '80.40%'}, {'id': 5, 'label': '负面情感比例', 'value': '0.25%'}]}]}, 'section2': {'type': 'section', 'title': 'AI总结', 'description': '【整体情况】\\n权威背书强势，理想汽车通过央视新闻和明星（朱广权、岳云鹏）的参与获得了强有力的品牌背书，展现强技术口碑。\\n【核心亮点】\\n权威背书强势...展现强技术口碑。\\n【风险预警】\\n预算管理...业形象的长尾影响。\\n', 'content': []}, 'section3': {'type': 'section', 'title': '观点分类', 'description': '', 'content': [{'type': 'pie', 'option': {'series': [{'data': [{'value': 67.0, 'name': '权威背书 67%'}, {'value': 21.0, 'name': '趣味互动 21%'}, {'value': 5.0, 'name': '预算管理争议 5%'}, {'value': 4.0, 'name': '家庭需求 4%'}]}]}}, {'type': 'bar:negative', 'option': {'title': {'text': '观点内容情感分布'}, 'legend': {'data': ['负向', '正向']}, 'xAxis': {'type': 'value'}, 'yAxis': {'type': 'category', 'data': ['权威背书', '趣味互动', '预算管理争议', '家庭需求']}, 'series': [{'name': '负向', 'data': [0, 0, 4, 0]}, {'name': '正向', 'data': [83, 76, 58, 72]}]}}, {'type': 'table', 'title': '观点详情', 'description': '各观点详细信息', 'columns': [{'prop': 'viewpoint', 'label': '观点'}, {'prop': 'explain', 'label': '解释说明'}, {'prop': 'positive', 'label': '正向'}, {'prop': 'negative', 'label': '负向'}], 'data': [{'id': 1, 'viewpoint': '权威背书', 'explain': '央视新闻和明星（如岳云鹏、朱广权）的参与和认可能为理想汽车提供了权威背书，增强了品牌公信力，并展示其技术领先性。', 'positive': '-朱广权点赞理想MEGA的\\"公路高铁级\\"静谧性\\n-岳云鹏称车内安静得像魔法\\n-央视新闻为理想汽车技术实力提供有力背书\\n-朱广权肯定激光雷达实现的全天候主动安全能力\\n-岳云鹏演示车载语音助手\\"理想同学\\"识别方言', 'negative': '-理想汽车与央视合作被质疑噱头大于价值'}, {'id': 2, 'viewpoint': '趣味互动', 'explain': '直播中的幽默互动（如理想同学的接梗能力、岳云鹏和朱广权的表现）受到网友广泛好评，认为形式生动有趣且展现了科技魅力。', 'positive': '-李想、朱广权、岳云鹏，这阵容太豪华！\\n-车载语音助手今晚报位出道！\\n-直播期间车载语音助手直接封神！\\n-理想同学比朱广权老师还有梗的！\\n-这直播太有意思了！岳云鹏和朱广权的组合太绝了', 'negative': ''}, {'id': 3, 'viewpoint': '预算管理争议', 'explain': '李想严格审批预算的习惯引发讨论，部分网友认可其节约作风，但也有人质疑管理方式，同时存在单纯赞美（如\\"666\\"）的声音。', 'positive': '-谨慎就对了不要不怎么经营好企业\\n-只有这样做，理想汽车才会上走得更高更远的巅峰[哇]\\n-预算把控严格程度上来说绝对是为消费者负责\\n-公司大了，各个环节稍微松一松，很多钱就出去了。相当轻微紧一紧，也能节省很多钱\\n-理想的活动邀请基本看不到两类汽车博主', 'negative': '-市值两千多亿的企业，两万元的费用都要老板自己批\\n-2万块都要亲自审批，有李想这样的老板打工人享福了\\n-难怪呢，其实我之前参加各品牌的活动，就注意到一个现象\\n-抠厂实至名归\\n-年度\\"最抠\\"？某新势力CEO：2万元都自己审批'}, {'id': 4, 'viewpoint': '家庭需求', 'explain': '网友高度评价理想汽车对家庭用户（如儿童安全、舒适性和智能化功能）的重视，认为其是理想的家庭用车选择。', 'positive': '-理想汽车创造幸福的家\\n-理想L9的舒适性能满足母亲需要\\n-理想汽车真的很注重家庭\\n-理想汽车是家的定位，更加注重家人的完全\\n-理想汽车对孩子对家人的保护绝对是首屈一指的', 'negative': ''}]}]}}
2025-08-11 16:11:23 - BrandEventTools - INFO - Report generation payload: {"section1": {"type": "section", "title": "基本信息", "description": "", "content": [{"type": "descriptions", "data": [{"id": 1, "label": "统计时间", "value": "截止5月16日16:00"}, {"id": 2, "label": "内容量", "value": 14069}, {"id": 3, "label": "口碑指数", "value": "80.15%"}, {"id": 4, "label": "正面情感比例", "value": "80.40%"}, {"id": 5, "label": "负面情感比例", "value": "0.25%"}]}]}, "section2": {"type": "section", "title": "AI总结", "description": "【整体情况】\\n权威背书强势，理想汽车通过央视新闻和明星（朱广权、岳云鹏）的参与获得了强有力的品牌背书，展现强技术口碑。\\n【核心亮点】\\n权威背书强势...展现强技术口碑。\\n【风险预警】\\n预算管理...业形象的长尾影响。\\n", "content": []}, "section3": {"type": "section", "title": "观点分类", "description": "", "content": [{"type": "pie", "option": {"series": [{"data": [{"value": 67.0, "name": "权威背书 67%"}, {"value": 21.0, "name": "趣味互动 21%"}, {"value": 5.0, "name": "预算管理争议 5%"}, {"value": 4.0, "name": "家庭需求 4%"}]}]}}, {"type": "bar:negative", "option": {"title": {"text": "观点内容情感分布"}, "legend": {"data": ["负向", "正向"]}, "xAxis": {"type": "value"}, "yAxis": {"type": "category", "data": ["权威背书", "趣味互动", "预算管理争议", "家庭需求"]}, "series": [{"name": "负向", "data": [0, 0, 4, 0]}, {"name": "正向", "data": [83, 76, 58, 72]}]}}, {"type": "table", "title": "观点详情", "description": "各观点详细信息", "columns": [{"prop": "viewpoint", "label": "观点"}, {"prop": "explain", "label": "解释说明"}, {"prop": "positive", "label": "正向"}, {"prop": "negative", "label": "负向"}], "data": [{"id": 1, "viewpoint": "权威背书", "explain": "央视新闻和明星（如岳云鹏、朱广权）的参与和认可能为理想汽车提供了权威背书，增强了品牌公信力，并展示其技术领先性。", "positive": "-朱广权点赞理想MEGA的\\\"公路高铁级\\\"静谧性\\n-岳云鹏称车内安静得像魔法\\n-央视新闻为理想汽车技术实力提供有力背书\\n-朱广权肯定激光雷达实现的全天候主动安全能力\\n-岳云鹏演示车载语音助手\\\"理想同学\\\"识别方言", "negative": "-理想汽车与央视合作被质疑噱头大于价值"}, {"id": 2, "viewpoint": "趣味互动", "explain": "直播中的幽默互动（如理想同学的接梗能力、岳云鹏和朱广权的表现）受到网友广泛好评，认为形式生动有趣且展现了科技魅力。", "positive": "-李想、朱广权、岳云鹏，这阵容太豪华！\\n-车载语音助手今晚报位出道！\\n-直播期间车载语音助手直接封神！\\n-理想同学比朱广权老师还有梗的！\\n-这直播太有意思了！岳云鹏和朱广权的组合太绝了", "negative": ""}, {"id": 3, "viewpoint": "预算管理争议", "explain": "李想严格审批预算的习惯引发讨论，部分网友认可其节约作风，但也有人质疑管理方式，同时存在单纯赞美（如\\\"666\\\"）的声音。", "positive": "-谨慎就对了不要不怎么经营好企业\\n-只有这样做，理想汽车才会上走得更高更远的巅峰[哇]\\n-预算把控严格程度上来说绝对是为消费者负责\\n-公司大了，各个环节稍微松一松，很多钱就出去了。相当轻微紧一紧，也能节省很多钱\\n-理想的活动邀请基本看不到两类汽车博主", "negative": "-市值两千多亿的企业，两万元的费用都要老板自己批\\n-2万块都要亲自审批，有李想这样的老板打工人享福了\\n-难怪呢，其实我之前参加各品牌的活动，就注意到一个现象\\n-抠厂实至名归\\n-年度\\\"最抠\\\"？某新势力CEO：2万元都自己审批"}, {"id": 4, "viewpoint": "家庭需求", "explain": "网友高度评价理想汽车对家庭用户（如儿童安全、舒适性和智能化功能）的重视，认为其是理想的家庭用车选择。", "positive": "-理想汽车创造幸福的家\\n-理想L9的舒适性能满足母亲需要\\n-理想汽车真的很注重家庭\\n-理想汽车是家的定位，更加注重家人的完全\\n-理想汽车对孩子对家人的保护绝对是首屈一指的", "negative": ""}]}]}}
2025-08-11 16:11:23 - BrandEventTools - INFO - Report generated successfully
2025-08-11 16:11:23 - brand_event.reportnode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [ReportNode] Starting HTML upload to S3
2025-08-11 16:11:23 - BrandEventTools - INFO - Starting HTML upload to S3
2025-08-11 16:11:23 - BrandEventTools - INFO - HTML uploaded successfully: {'fileKey': 'backend/report-2fj4oVeWNyIsUx4omPtha1-20250811-161123.html'}
2025-08-11 16:11:23 - brand_event.reportnode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [ReportNode] HTML uploaded successfully: {'fileKey': 'backend/report-2fj4oVeWNyIsUx4omPtha1-20250811-161123.html'}
2025-08-11 16:11:25 - root - INFO - Found AI summary in section: AI总结
2025-08-11 16:11:25 - brand_event.reportnode - INFO - [ReportNode] Parsed DSL summary: AI summary > 【整体情况】\n权威背书强势，理想汽车通过央视新闻和明星（朱广权、岳云鹏）的参与获得了强有力的品牌背书，展现强技术口碑。\n【核心亮点】\n权威背书强势...展现强技术口碑。\n【风险预警】\n预算管理...业形象的长尾影响。\n
2025-08-11 16:11:26 - brand_event.reportnode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [ReportNode] Report generation completed
2025-08-11 16:11:27 - agentops_event_sdk_python.subscribers.standard - INFO - Event[9d374b92-7f2c-4bb6-af19-7bf13664d34a] sent in 0.02s
2025-08-11 16:11:27 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.CHAT> 💬 Assistant: 正在生成分析报告......
2025-08-11 16:11:27 - src.messages.event_handler - INFO - 发送Assistant ChatEvent: 正在生成分析报告...... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:27 - agentops_event_sdk_python.subscribers.standard - INFO - Event[a050d00c-26f0-41c3-936e-acc5482d3cac] sent in 0.09s
2025-08-11 16:11:27 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在调用外部报告服务...
2025-08-11 16:11:27 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在调用外部报告服务...... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:28 - agentops_event_sdk_python.subscribers.standard - INFO - Event[0b53d479-d095-44c5-91eb-ae018218dff2] sent in 0.11s
2025-08-11 16:11:28 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在上传HTML报告到云存储...
2025-08-11 16:11:28 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在上传HTML报告到云存储...... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:28 - agentops_event_sdk_python.subscribers.standard - INFO - Event[899b9f7d-99dd-4343-9811-12f18b4ce1c2] sent in 0.09s
2025-08-11 16:11:28 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在解析报告中的AI总结和关键数据...
2025-08-11 16:11:28 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在解析报告中的AI总结和关键数据...... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:28 - agentops_event_sdk_python.subscribers.standard - INFO - Event[bbc8eb82-6c54-4adb-9b22-1a8cc3282bb8] sent in 0.09s
2025-08-11 16:11:28 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.CHAT> 💬 Assistant: 已生成HTML可视化图表，请查收。

AI总结

【整体情况】\n权威背书强势，理想汽车通过央视新闻和明星（朱广权、岳云鹏）的参与获得了强有力的品牌背书，展现强技术口碑。\n【核心亮点】\n权威背书强...
2025-08-11 16:11:28 - src.messages.event_handler - INFO - 发送Assistant ChatEvent: 已生成HTML可视化图表，请查收。

AI总结

【整体情况】\n权威背书强势，理想汽车通过央视新闻... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:28 - agentops_event_sdk_python.subscribers.standard - INFO - Event[af7fec93-b991-420e-b8e4-f02a0acb38ac] sent in 0.09s
2025-08-11 16:11:28 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.CHAT> 💬 Assistant: 报告文件...
2025-08-11 16:11:28 - src.messages.event_handler - INFO - 发送Assistant ChatEvent: 报告文件... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:28 - agentops_event_sdk_python.subscribers.standard - INFO - Event[feb9b644-16ec-40d2-bd5b-43a07ac9b653] sent in 0.09s
2025-08-11 16:11:28 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.STATUS_UPDATE> 📄 Event: AgentStatusEvent
2025-08-11 16:11:28 - src.messages.event_handler - INFO - 发送AgentStatusEvent: idle - 等待用户确认 | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:28 - brand_event.api - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [API] Chat request completed successfully
2025-08-11 16:11:56 - brand_event.api - INFO - [Session:3iElrkaGY2t0EdIdPbgN8S] [API] Processing chat request
2025-08-11 16:11:56 - brand_event.supervisornode - INFO - [Session:3iElrkaGY2t0EdIdPbgN8S] [SupervisorNode] Start doing workflow supervision
2025-08-11 16:11:58 - httpx - INFO - HTTP Request: POST https://llm-model-proxy.dev.fc.chj.cloud/agentops/chat/completions "HTTP/1.1 200 OK"
2025-08-11 16:11:58 - brand_event.supervisornode - INFO - [Session:3iElrkaGY2t0EdIdPbgN8S] [SupervisorNode] User message analysis: type=task
2025-08-11 16:11:58 - brand_event.supervisornode - INFO - [Session:3iElrkaGY2t0EdIdPbgN8S] [SupervisorNode] LLM routing decision: report, reason: 用户请求与当前工作流状态相符，需进行报告处理
2025-08-11 16:11:58 - brand_event.supervisornode - INFO - [Session:3iElrkaGY2t0EdIdPbgN8S] [SupervisorNode] Completed workflow supervision, result: route to report, reason: 用户请求与当前工作流状态相符，需进行报告处理
2025-08-11 16:11:58 - agentops_event_sdk_python.subscribers.standard - INFO - Event[1197c6c3-2d27-4142-8ae3-2f453a2f0a31] sent in 0.06s
2025-08-11 16:11:58 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在分析当前状态和用户消息...
2025-08-11 16:11:58 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在分析当前状态和用户消息...... | session_id: 3iElrkaGY2t0EdIdPbgN8S, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:58 - agentops_event_sdk_python.subscribers.standard - INFO - Event[b2da8337-f025-43bb-a7bd-727b413520c9] sent in 0.03s
2025-08-11 16:11:58 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在分析用户消息类型和意图...
2025-08-11 16:11:58 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在分析用户消息类型和意图...... | session_id: 3iElrkaGY2t0EdIdPbgN8S, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:58 - brand_event.reportnode - INFO - [Session:3iElrkaGY2t0EdIdPbgN8S] [ReportNode] Starting final report generation
2025-08-11 16:11:58 - brand_event.reportnode - INFO - [Session:3iElrkaGY2t0EdIdPbgN8S] [ReportNode] Starting report generation
2025-08-11 16:11:58 - BrandEventTools - INFO - Generating HTML report from DSL data
2025-08-11 16:11:58 - BrandEventTools - INFO - Report generation dsl_data: {'section1': {'type': 'section', 'title': '基本信息', 'description': '', 'content': [{'type': 'descriptions', 'data': [{'id': 1, 'label': '统计时间', 'value': '截止5月16日16:00'}, {'id': 2, 'label': '内容量', 'value': 14069}, {'id': 3, 'label': '口碑指数', 'value': '80.15%'}, {'id': 4, 'label': '正面情感比例', 'value': '80.40%'}, {'id': 5, 'label': '负面情感比例', 'value': '0.25%'}]}]}, 'section2': {'type': 'section', 'title': 'AI总结', 'description': '【整体情况】\\n权威背书强势，理想汽车通过央视新闻和明星（朱广权、岳云鹏）的参与获得了强有力的品牌背书，展现强技术口碑。\\n【核心亮点】\\n权威背书强势...展现强技术口碑。\\n【风险预警】\\n预算管理...业形象的长尾影响。\\n', 'content': []}, 'section3': {'type': 'section', 'title': '观点分类', 'description': '', 'content': [{'type': 'pie', 'option': {'series': [{'data': [{'value': 67.0, 'name': '权威背书 67%'}, {'value': 21.0, 'name': '趣味互动 21%'}, {'value': 5.0, 'name': '预算管理争议 5%'}, {'value': 4.0, 'name': '家庭需求 4%'}]}]}}, {'type': 'bar:negative', 'option': {'title': {'text': '观点内容情感分布'}, 'legend': {'data': ['负向', '正向']}, 'xAxis': {'type': 'value'}, 'yAxis': {'type': 'category', 'data': ['权威背书', '趣味互动', '预算管理争议', '家庭需求']}, 'series': [{'name': '负向', 'data': [0, 0, 4, 0]}, {'name': '正向', 'data': [83, 76, 58, 72]}]}}, {'type': 'table', 'title': '观点详情', 'description': '各观点详细信息', 'columns': [{'prop': 'viewpoint', 'label': '观点'}, {'prop': 'explain', 'label': '解释说明'}, {'prop': 'positive', 'label': '正向'}, {'prop': 'negative', 'label': '负向'}], 'data': [{'id': 1, 'viewpoint': '权威背书', 'explain': '央视新闻和明星（如岳云鹏、朱广权）的参与和认可能为理想汽车提供了权威背书，增强了品牌公信力，并展示其技术领先性。', 'positive': '-朱广权点赞理想MEGA的\\"公路高铁级\\"静谧性\\n-岳云鹏称车内安静得像魔法\\n-央视新闻为理想汽车技术实力提供有力背书\\n-朱广权肯定激光雷达实现的全天候主动安全能力\\n-岳云鹏演示车载语音助手\\"理想同学\\"识别方言', 'negative': '-理想汽车与央视合作被质疑噱头大于价值'}, {'id': 2, 'viewpoint': '趣味互动', 'explain': '直播中的幽默互动（如理想同学的接梗能力、岳云鹏和朱广权的表现）受到网友广泛好评，认为形式生动有趣且展现了科技魅力。', 'positive': '-李想、朱广权、岳云鹏，这阵容太豪华！\\n-车载语音助手今晚报位出道！\\n-直播期间车载语音助手直接封神！\\n-理想同学比朱广权老师还有梗的！\\n-这直播太有意思了！岳云鹏和朱广权的组合太绝了', 'negative': ''}, {'id': 3, 'viewpoint': '预算管理争议', 'explain': '李想严格审批预算的习惯引发讨论，部分网友认可其节约作风，但也有人质疑管理方式，同时存在单纯赞美（如\\"666\\"）的声音。', 'positive': '-谨慎就对了不要不怎么经营好企业\\n-只有这样做，理想汽车才会上走得更高更远的巅峰[哇]\\n-预算把控严格程度上来说绝对是为消费者负责\\n-公司大了，各个环节稍微松一松，很多钱就出去了。相当轻微紧一紧，也能节省很多钱\\n-理想的活动邀请基本看不到两类汽车博主', 'negative': '-市值两千多亿的企业，两万元的费用都要老板自己批\\n-2万块都要亲自审批，有李想这样的老板打工人享福了\\n-难怪呢，其实我之前参加各品牌的活动，就注意到一个现象\\n-抠厂实至名归\\n-年度\\"最抠\\"？某新势力CEO：2万元都自己审批'}, {'id': 4, 'viewpoint': '家庭需求', 'explain': '网友高度评价理想汽车对家庭用户（如儿童安全、舒适性和智能化功能）的重视，认为其是理想的家庭用车选择。', 'positive': '-理想汽车创造幸福的家\\n-理想L9的舒适性能满足母亲需要\\n-理想汽车真的很注重家庭\\n-理想汽车是家的定位，更加注重家人的完全\\n-理想汽车对孩子对家人的保护绝对是首屈一指的', 'negative': ''}]}]}}
2025-08-11 16:11:58 - BrandEventTools - INFO - Report generation payload: {"section1": {"type": "section", "title": "基本信息", "description": "", "content": [{"type": "descriptions", "data": [{"id": 1, "label": "统计时间", "value": "截止5月16日16:00"}, {"id": 2, "label": "内容量", "value": 14069}, {"id": 3, "label": "口碑指数", "value": "80.15%"}, {"id": 4, "label": "正面情感比例", "value": "80.40%"}, {"id": 5, "label": "负面情感比例", "value": "0.25%"}]}]}, "section2": {"type": "section", "title": "AI总结", "description": "【整体情况】\\n权威背书强势，理想汽车通过央视新闻和明星（朱广权、岳云鹏）的参与获得了强有力的品牌背书，展现强技术口碑。\\n【核心亮点】\\n权威背书强势...展现强技术口碑。\\n【风险预警】\\n预算管理...业形象的长尾影响。\\n", "content": []}, "section3": {"type": "section", "title": "观点分类", "description": "", "content": [{"type": "pie", "option": {"series": [{"data": [{"value": 67.0, "name": "权威背书 67%"}, {"value": 21.0, "name": "趣味互动 21%"}, {"value": 5.0, "name": "预算管理争议 5%"}, {"value": 4.0, "name": "家庭需求 4%"}]}]}}, {"type": "bar:negative", "option": {"title": {"text": "观点内容情感分布"}, "legend": {"data": ["负向", "正向"]}, "xAxis": {"type": "value"}, "yAxis": {"type": "category", "data": ["权威背书", "趣味互动", "预算管理争议", "家庭需求"]}, "series": [{"name": "负向", "data": [0, 0, 4, 0]}, {"name": "正向", "data": [83, 76, 58, 72]}]}}, {"type": "table", "title": "观点详情", "description": "各观点详细信息", "columns": [{"prop": "viewpoint", "label": "观点"}, {"prop": "explain", "label": "解释说明"}, {"prop": "positive", "label": "正向"}, {"prop": "negative", "label": "负向"}], "data": [{"id": 1, "viewpoint": "权威背书", "explain": "央视新闻和明星（如岳云鹏、朱广权）的参与和认可能为理想汽车提供了权威背书，增强了品牌公信力，并展示其技术领先性。", "positive": "-朱广权点赞理想MEGA的\\\"公路高铁级\\\"静谧性\\n-岳云鹏称车内安静得像魔法\\n-央视新闻为理想汽车技术实力提供有力背书\\n-朱广权肯定激光雷达实现的全天候主动安全能力\\n-岳云鹏演示车载语音助手\\\"理想同学\\\"识别方言", "negative": "-理想汽车与央视合作被质疑噱头大于价值"}, {"id": 2, "viewpoint": "趣味互动", "explain": "直播中的幽默互动（如理想同学的接梗能力、岳云鹏和朱广权的表现）受到网友广泛好评，认为形式生动有趣且展现了科技魅力。", "positive": "-李想、朱广权、岳云鹏，这阵容太豪华！\\n-车载语音助手今晚报位出道！\\n-直播期间车载语音助手直接封神！\\n-理想同学比朱广权老师还有梗的！\\n-这直播太有意思了！岳云鹏和朱广权的组合太绝了", "negative": ""}, {"id": 3, "viewpoint": "预算管理争议", "explain": "李想严格审批预算的习惯引发讨论，部分网友认可其节约作风，但也有人质疑管理方式，同时存在单纯赞美（如\\\"666\\\"）的声音。", "positive": "-谨慎就对了不要不怎么经营好企业\\n-只有这样做，理想汽车才会上走得更高更远的巅峰[哇]\\n-预算把控严格程度上来说绝对是为消费者负责\\n-公司大了，各个环节稍微松一松，很多钱就出去了。相当轻微紧一紧，也能节省很多钱\\n-理想的活动邀请基本看不到两类汽车博主", "negative": "-市值两千多亿的企业，两万元的费用都要老板自己批\\n-2万块都要亲自审批，有李想这样的老板打工人享福了\\n-难怪呢，其实我之前参加各品牌的活动，就注意到一个现象\\n-抠厂实至名归\\n-年度\\\"最抠\\\"？某新势力CEO：2万元都自己审批"}, {"id": 4, "viewpoint": "家庭需求", "explain": "网友高度评价理想汽车对家庭用户（如儿童安全、舒适性和智能化功能）的重视，认为其是理想的家庭用车选择。", "positive": "-理想汽车创造幸福的家\\n-理想L9的舒适性能满足母亲需要\\n-理想汽车真的很注重家庭\\n-理想汽车是家的定位，更加注重家人的完全\\n-理想汽车对孩子对家人的保护绝对是首屈一指的", "negative": ""}]}]}}
2025-08-11 16:11:58 - BrandEventTools - INFO - Report generated successfully
2025-08-11 16:11:58 - brand_event.reportnode - INFO - [Session:3iElrkaGY2t0EdIdPbgN8S] [ReportNode] Starting HTML upload to S3
2025-08-11 16:11:58 - BrandEventTools - INFO - Starting HTML upload to S3
2025-08-11 16:11:58 - BrandEventTools - INFO - HTML uploaded successfully: {'fileKey': 'backend/report-3iElrkaGY2t0EdIdPbgN8S-20250811-161158.html'}
2025-08-11 16:11:58 - brand_event.reportnode - INFO - [Session:3iElrkaGY2t0EdIdPbgN8S] [ReportNode] HTML uploaded successfully: {'fileKey': 'backend/report-3iElrkaGY2t0EdIdPbgN8S-20250811-161158.html'}
2025-08-11 16:11:58 - root - INFO - Found AI summary in section: AI总结
2025-08-11 16:11:58 - brand_event.reportnode - INFO - [ReportNode] Parsed DSL summary: AI summary > 【整体情况】\n权威背书强势，理想汽车通过央视新闻和明星（朱广权、岳云鹏）的参与获得了强有力的品牌背书，展现强技术口碑。\n【核心亮点】\n权威背书强势...展现强技术口碑。\n【风险预警】\n预算管理...业形象的长尾影响。\n
2025-08-11 16:11:58 - brand_event.reportnode - INFO - [Session:3iElrkaGY2t0EdIdPbgN8S] [ReportNode] Report generation completed
2025-08-11 16:11:58 - agentops_event_sdk_python.subscribers.standard - INFO - Event[b5382911-d45a-478f-a68d-03994f9d4a81] sent in 0.03s
2025-08-11 16:11:58 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.CHAT> 💬 Assistant: 正在生成分析报告......
2025-08-11 16:11:58 - src.messages.event_handler - INFO - 发送Assistant ChatEvent: 正在生成分析报告...... | session_id: 3iElrkaGY2t0EdIdPbgN8S, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:58 - agentops_event_sdk_python.subscribers.standard - INFO - Event[63810afc-8a34-48ab-9dd1-b518c08ecdbc] sent in 0.19s
2025-08-11 16:11:58 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在调用外部报告服务...
2025-08-11 16:11:58 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在调用外部报告服务...... | session_id: 3iElrkaGY2t0EdIdPbgN8S, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:58 - agentops_event_sdk_python.subscribers.standard - INFO - Event[a3b1a03e-6d50-42ab-89a8-ad2af0c99c52] sent in 0.03s
2025-08-11 16:11:58 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在上传HTML报告到云存储...
2025-08-11 16:11:58 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在上传HTML报告到云存储...... | session_id: 3iElrkaGY2t0EdIdPbgN8S, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:59 - agentops_event_sdk_python.subscribers.standard - INFO - Event[d92a4824-858c-4cb2-a52e-0b7166309a89] sent in 0.06s
2025-08-11 16:11:59 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在解析报告中的AI总结和关键数据...
2025-08-11 16:11:59 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在解析报告中的AI总结和关键数据...... | session_id: 3iElrkaGY2t0EdIdPbgN8S, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:59 - agentops_event_sdk_python.subscribers.standard - INFO - Event[fda96ed1-9372-4376-bfac-a51895f09a5c] sent in 0.11s
2025-08-11 16:11:59 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.CHAT> 💬 Assistant: 已生成HTML可视化图表，请查收。

AI总结

【整体情况】\n权威背书强势，理想汽车通过央视新闻和明星（朱广权、岳云鹏）的参与获得了强有力的品牌背书，展现强技术口碑。\n【核心亮点】\n权威背书强...
2025-08-11 16:11:59 - src.messages.event_handler - INFO - 发送Assistant ChatEvent: 已生成HTML可视化图表，请查收。

AI总结

【整体情况】\n权威背书强势，理想汽车通过央视新闻... | session_id: 3iElrkaGY2t0EdIdPbgN8S, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:59 - agentops_event_sdk_python.subscribers.standard - INFO - Event[20b395eb-8679-4cac-a902-1e207d08206d] sent in 0.03s
2025-08-11 16:11:59 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.CHAT> 💬 Assistant: 报告文件...
2025-08-11 16:11:59 - src.messages.event_handler - INFO - 发送Assistant ChatEvent: 报告文件... | session_id: 3iElrkaGY2t0EdIdPbgN8S, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:59 - agentops_event_sdk_python.subscribers.standard - INFO - Event[f8c6fa8a-5646-48e2-b836-981b48dd89aa] sent in 0.10s
2025-08-11 16:11:59 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.STATUS_UPDATE> 📄 Event: AgentStatusEvent
2025-08-11 16:11:59 - src.messages.event_handler - INFO - 发送AgentStatusEvent: idle - 等待用户确认 | session_id: 3iElrkaGY2t0EdIdPbgN8S, sandbox_id: None, task_id: yYaDsDFErnKZOaE3h3y3c
2025-08-11 16:11:59 - brand_event.api - INFO - [Session:3iElrkaGY2t0EdIdPbgN8S] [API] Chat request completed successfully
