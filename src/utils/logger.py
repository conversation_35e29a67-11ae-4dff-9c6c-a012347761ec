"""
Workflow日志工具
参考utils/logging.py的设计思路，为workflow提供统一的日志格式和消息管理
"""

import logging
from typing import Optional, Any, Dict
from functools import wraps
from src.utils.log_config import ensure_logging_configured


class WorkflowLogger:
    """
    工作流日志器
    
    参考SpecificLogger的设计思路，提供统一的日志格式和标准化的日志消息
    """
    
    def __init__(self, component_name: str, logger_name: Optional[str] = None):
        """
        初始化工作流日志器
        
        Args:
            component_name: 组件名称 (如 'SupervisorNode', 'ExecutionNode', 'ReportNode')
            logger_name: 自定义日志器名称，默认为 'brand_event.{component_name.lower()}'
        """
        # 确保日志系统已配置
        ensure_logging_configured()
        
        self.component_name = component_name
        self.logger_name = logger_name or f"brand_event.{component_name.lower()}"
        self.logger = logging.getLogger(self.logger_name)
    
    def _format_message(self, message: str, session_id: Optional[str] = None, 
                       context: Optional[Dict[str, Any]] = None) -> str:
        """
        格式化日志消息，与SpecificLogger保持相同的格式
        
        Args:
            message: 日志消息
            session_id: 可选的会话ID
            context: 可选的额外上下文
            
        Returns:
            格式化后的消息字符串
        """
        parts = []
        
        if session_id:
            parts.append(f"[Session:{session_id}]")
        
        parts.append(f"[{self.component_name}]")
        
        if context:
            for key, value in context.items():
                parts.append(f"[{key}:{value}]")
        
        parts.append(message)
        return " ".join(parts)
    
    def debug(self, message: str, session_id: Optional[str] = None, 
              context: Optional[Dict[str, Any]] = None):
        """记录debug级别日志"""
        formatted_msg = self._format_message(message, session_id, context)
        self.logger.debug(formatted_msg)
    
    def info(self, message: str, session_id: Optional[str] = None, 
             context: Optional[Dict[str, Any]] = None):
        """记录info级别日志"""
        formatted_msg = self._format_message(message, session_id, context)
        self.logger.info(formatted_msg)
    
    def warning(self, message: str, session_id: Optional[str] = None, 
                context: Optional[Dict[str, Any]] = None):
        """记录warning级别日志"""
        formatted_msg = self._format_message(message, session_id, context)
        self.logger.warning(formatted_msg)
    
    def error(self, message: str, session_id: Optional[str] = None, 
              context: Optional[Dict[str, Any]] = None, exc_info: bool = False):
        """记录error级别日志"""
        formatted_msg = self._format_message(message, session_id, context)
        self.logger.error(formatted_msg, exc_info=exc_info)
    
    def critical(self, message: str, session_id: Optional[str] = None, 
                 context: Optional[Dict[str, Any]] = None):
        """记录critical级别日志"""
        formatted_msg = self._format_message(message, session_id, context)
        self.logger.critical(formatted_msg)


def get_workflow_logger(component_name: str, logger_name: Optional[str] = None) -> WorkflowLogger:
    """
    获取工作流日志器的便捷函数
    
    Args:
        component_name: 组件名称
        logger_name: 可选的自定义日志器名称
        
    Returns:
        WorkflowLogger实例
    """
    return WorkflowLogger(component_name, logger_name)


# 标准日志消息模板
class LogMessages:
    """
    标准日志消息模板
    参考utils/logging.py中的LogMessages，但专门为workflow设计
    """
    
    # Workflow operations
    WORKFLOW_SUPERVISION_START = "Start doing workflow supervision"
    WORKFLOW_SUPERVISION_COMPLETE = "Completed workflow supervision, result: route to {destination}, reason: {reason}"
    WORKFLOW_SUPERVISION_FAILED = "Failed workflow supervision, error: {error}"

    WORKFLOW_INTENT_START = "Start doing intent clarification (round {round})"
    WORKFLOW_INTENT_COMPLETE = "Completed intent clarification, result: {result}"
    WORKFLOW_INTENT_FAILED = "Failed intent clarification, error: {error}"
    
    WORKFLOW_INTENT_START = "Start doing intent clarification (round {round})"
    WORKFLOW_INTENT_COMPLETE = "Completed intent clarification, result: {result}"
    WORKFLOW_INTENT_FAILED = "Failed intent clarification, error: {error}"
    
    WORKFLOW_PLANNING_START = "Start doing planning (round {round})"
    WORKFLOW_PLANNING_COMPLETE = "Completed planning, result: {result}"
    WORKFLOW_PLANNING_FAILED = "Failed planning, error: {error}"
    WORKFLOW_PLANNING_INPUT = " Plan INPUT,session_id={session_id},clarification_result={clarification_result},intent_summary={intent_summary}"


    WORKFLOW_EXECUTION_START = "Start doing task execution ({total_steps} steps)"
    WORKFLOW_EXECUTION_COMPLETE = "Execution completed"
    WORKFLOW_EXECUTION_FAILED = "Execution failed: {error}"
    
    WORKFLOW_REPORT_START = "Starting final report generation"
    WORKFLOW_REPORT_COMPLETE = "Report generation completed"
    WORKFLOW_REPORT_FAILED = "Failed to generate report: {error}"
    
    # Step operations
    STEP_START = "Executing step {step_index}: {step_title}"
    STEP_COMPLETE = "Completed step: {step_title}"
    STEP_FAILED = "Step {step_title} failed: {error}"
    STEP_SKIPPED = "Skipping step {step_index}: {step_title}"
    
    # User interactions
    USER_MESSAGE_ANALYSIS = "User message analysis: type={message_type}"
    USER_INTENT_ANALYSIS = "User intent analysis: type={intent_type}"
    USER_FEEDBACK_RECEIVED = "User feedback received, re-analyzing requirements"
    USER_PLAN_APPROVED = "User approved plan, starting execution"
    USER_PLAN_MODIFICATION = "User provided plan modification suggestions, re-planning"

    # API operations
    CHAT_REQUEST_START = "Processing chat request"
    CHAT_REQUEST_COMPLETE = "Chat request completed successfully"
    CHAT_REQUEST_FAILED = "Chat request failed: {error}"
    SESSION_CREATED = "New session created: {session_id}"
    SESSION_CONTINUED = "Continuing existing session: {session_id}"
    SESSION_DELETED = "Session deleted: {session_id}"
    API_INITIALIZED = "API initialized successfully"
    
    # System operations
    LLM_ROUTING_DECISION = "LLM routing decision: {next}, reason: {reason}"
    JWT_TOKEN_GENERATED = "Generated dynamic JWT token for user: {user_id}"
    MCP_TOOLS_LOADED = "Created React agent with {tool_count} MCP tools"
    MCP_SETUP_FAILED = "Failed to setup React agent: {error}, fallback to plain LLM"
    REPORT_SERVICE_CALL = "Starting report generation"
    S3_UPLOAD_START = "Starting HTML upload to S3"
    S3_UPLOAD_SUCCESS = "HTML uploaded successfully: {result}"
    S3_UPLOAD_FAILED = "HTML upload failed: {error}"
    DSL_PARSING = "Parsed DSL summary: AI summary > {ai_summary}"
    
    # Errors
    ERROR_NO_EXECUTION_PLAN = "Failed task execution, error: no execution plan provided"
    ERROR_INVALID_EXECUTION_PLAN = "Failed task execution, error: invalid execution plan"
    ERROR_LLM_ROUTING_FAILED = "Failed LLM-based routing, error: {error}"
    ERROR_INTENT_ANALYSIS_FAILED = "Failed to analyze user intent: {error}"
    ERROR_INTENT_CLARITY_FAILED = "Failed to analyze intent clarity: {error}"
    ERROR_PLAN_FEEDBACK_FAILED = "Failed to analyze plan feedback intent: {error}"
    ERROR_PLAN_CREATION_FAILED = "Failed to create execution plan: {error}"
    ERROR_STEP_EXECUTION_FAILED = "Step {step_index} failed: {error}"
    ERROR_REPORT_GENERATION_FAILED = "Report generation failed: {error}"
    ERROR_DSL_PARSING_FAILED = "Error parsing DSL summary: {error}"


def log_workflow_operation(operation_name: str, include_args: bool = False):
    """
    装饰器：自动记录工作流操作的开始和完成
    
    参考utils/logging.py中的log_operation装饰器
    
    Args:
        operation_name: 操作名称
        include_args: 是否包含函数参数在日志中
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 尝试从参数中提取session_id
            session_id = None
            if len(args) > 0 and isinstance(args[0], dict) and 'session_id' in args[0]:
                session_id = args[0]['session_id']
            elif 'session_id' in kwargs:
                session_id = kwargs['session_id']
            elif len(args) > 1 and isinstance(args[1], dict) and 'session_id' in args[1]:
                session_id = args[1]['session_id']
            
            # 获取组件名称
            component_name = func.__name__.replace('_node', '').replace('_', ' ').title() + 'Node'
            logger = get_workflow_logger(component_name)
            
            # 记录开始
            start_msg = f"Starting {operation_name}"
            if include_args and args[1:]:
                start_msg += f" with args: {args[1:]}"
            logger.info(start_msg, session_id)
            
            try:
                result = func(*args, **kwargs)
                logger.info(f"Completed {operation_name} successfully", session_id)
                return result
            except Exception as e:
                logger.error(f"Failed {operation_name}: {str(e)}", session_id, exc_info=True)
                raise
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 尝试从参数中提取session_id
            session_id = None
            if len(args) > 0 and isinstance(args[0], dict) and 'session_id' in args[0]:
                session_id = args[0]['session_id']
            elif 'session_id' in kwargs:
                session_id = kwargs['session_id']
            elif len(args) > 1 and isinstance(args[1], dict) and 'session_id' in args[1]:
                session_id = args[1]['session_id']
            
            # 获取组件名称
            component_name = func.__name__.replace('_node', '').replace('_', ' ').title() + 'Node'
            logger = get_workflow_logger(component_name)
            
            # 记录开始
            start_msg = f"Starting {operation_name}"
            if include_args and args[1:]:
                start_msg += f" with args: {args[1:]}"
            logger.info(start_msg, session_id)
            
            try:
                result = await func(*args, **kwargs)
                logger.info(f"Completed {operation_name} successfully", session_id)
                return result
            except Exception as e:
                logger.error(f"Failed {operation_name}: {str(e)}", session_id, exc_info=True)
                raise
        
        # 根据函数是否为异步返回相应的包装器
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return wrapper
    
    return decorator


# 便捷别名
workflow_logger = get_workflow_logger
log_msg = LogMessages
