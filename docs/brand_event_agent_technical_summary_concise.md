# 品牌事件Agent技术总结文档（精简版）

## 📋 目录

1. [系统概览](#1-系统概览)
2. [系统架构](#2-系统架构)
3. [智能体架构](#3-智能体架构)
4. [执行流程](#4-执行流程)
5. [提示词设计](#5-提示词设计)
6. [高级特性](#6-高级特性)
7. [技术亮点](#7-技术亮点)

---

## 1. 系统概览

Brand-Event Agent 是基于 **LangGraph** 构建的多智能体舆情分析系统，采用状态驱动的工作流架构，通过5个专业化智能体协作完成品牌舆情分析任务。

### 核心特性
- **状态驱动工作流**: 9种状态精确控制业务流程
- **多智能体协作**: 5个专业化智能体分工协作
- **多轮交互**: 智能意图澄清和计划确认
- **暂停恢复**: 基于LangGraph的状态持久化
- **流式响应**: 实时状态更新优化用户体验

---

## 2. 系统架构

### 2.1 整体架构

```mermaid
graph TB
    subgraph "API层"
        API[FastAPI Gateway]
    end
    
    subgraph "工作流引擎与智能体层"
        WF[LangGraph Workflow]
        SM[状态管理器]
        SA[SupervisorAgent]
        IA[IntentAnalysisAgent]
        PA[PlanningAgent]
        EA[ExecutionAgent]
        RA[ReportAgent]
    end
    
    subgraph "工具集成层"
        MCP[MCP客户端]
        TOOLS[外部工具]
    end
    
    subgraph "存储层"
        MEM[内存存储]
        FILE[文件存储]
    end
    
    API --> WF
    WF --> SM
    WF --> SA
    SA --> IA
    SA --> PA
    SA --> EA
    SA --> RA
    EA --> MCP
    MCP --> TOOLS
    SM --> MEM
    RA --> FILE
```

### 2.2 技术栈
- **核心框架**: LangGraph 0.2.0+, LangChain 0.3.0+
- **API服务**: FastAPI 0.104.0+
- **数据验证**: Pydantic 2.5.0+
- **工具协议**: MCP (Model Context Protocol)
- **认证机制**: JWT动态令牌

---

## 3. 智能体架构

### 3.1 智能体分工

```mermaid
graph TD
    subgraph "协调层"
        SUP[SupervisorAgent<br/>路由决策]
    end
    
    subgraph "分析层"
        INT[IntentAnalysisAgent<br/>意图澄清]
        PLAN[PlanningAgent<br/>计划制定]
    end
    
    subgraph "执行层"
        EXEC[ExecutionAgent<br/>任务执行]
        REP[ReportAgent<br/>报告生成]
    end
    
    SUP --> INT
    SUP --> PLAN
    SUP --> EXEC
    SUP --> REP
```

### 3.2 核心智能体实现

#### SupervisorAgent (协调路由)
```python
class RoutingDecision(BaseModel):
    message_analysis: UserMessageType
    next: Literal["intent_clarification", "planning", "execution", "report", "__end__"]
    reason: str
    workflow_status: str

# 基于状态的路由规则
ROUTING_RULES = {
    "INITIALIZING": "intent_clarification",
    "CLARIFYING_INTENT": "intent_clarification", 
    "PLANNING": "planning",
    "EXECUTING": "execution",
    "REPORT": "report"
}
```

#### IntentAnalysisAgent (意图分析)
```python
class ClarificationResult(BaseModel):
    intent_clear: bool
    clarification_questions: List[str]
    clarification_result: Optional[Dict[str, Any]]
    response_message: str

# 必需信息检查
required_info = {
    "analysis_object": "具体品牌/产品/人物",
    "analysis_dimension": "声量/情感/传播/口碑等",
    "time_range": "时间范围（默认近一个月）",
    "platform_range": "平台范围（默认全平台）"
}
```

#### ExecutionAgent (任务执行)
```python
# MCP工具集成
def create_access_token(user_id: str) -> str:
    payload = {
        "sub": user_id,
        "exp": datetime.utcnow() + timedelta(minutes=2880),  # 48小时
        "type": "access"
    }
    return jwt.encode(payload, "brand_event", algorithm="HS256")

# 动态工具调用
mcp_client = MultiServerMCPClient(server_configs)
tools = await mcp_client.get_tools()
agent = create_react_agent(llm, tools)
```

---

## 4. 执行流程

### 4.1 状态驱动流程

```mermaid
stateDiagram-v2
    [*] --> INITIALIZING
    INITIALIZING --> CLARIFYING_INTENT
    CLARIFYING_INTENT --> CLARIFYING_INTENT : 需要澄清
    CLARIFYING_INTENT --> PLANNING : 意图明确
    PLANNING --> PLANNING : 计划修改
    PLANNING --> EXECUTING : 计划确认
    EXECUTING --> REPORT : 执行完成
    REPORT --> COMPLETED : 报告生成
    
    CLARIFYING_INTENT --> FAILED : 失败
    PLANNING --> FAILED : 失败
    EXECUTING --> FAILED : 失败
    REPORT --> FAILED : 失败
    
    COMPLETED --> [*]
    FAILED --> [*]
```

### 4.2 工作流节点

```python
def create_workflow_graph() -> StateGraph:
    builder = StateGraph(WorkflowState, config_schema=WorkflowConfiguration)
    
    # 添加节点
    builder.add_node("supervisor", supervisor_node)
    builder.add_node("intent_clarification", intent_clarification_node)
    builder.add_node("planning", planning_node)
    builder.add_node("execution", execution_node)
    builder.add_node("report", report_node)
    
    # 设置入口点
    builder.set_entry_point("supervisor")
    return builder

# 统一异常处理
@node_exception_handler("NodeName")
async def node_function(state: WorkflowState, writer: StreamWriter, config: RunnableConfig) -> Command:
    # 节点逻辑
    pass
```

---

## 5. 提示词设计

### 5.1 设计原则

- **角色定义明确**: 每个智能体都有清晰的职责定位
- **结构化输出**: 使用Pydantic模型确保格式一致
- **上下文感知**: 根据状态动态构建提示词
- **多语言支持**: 中英文国际
化消息系统

### 5.2 核心提示词模板

#### SupervisorAgent路由提示词
```python
def build_supervisor_routing_prompt(state) -> str:
    current_status = state.get("workflow_status", "initializing")
    user_input = state.get("user_input")
    
    return f"""
    请分析用户消息并决策下一步路由。
    
    当前工作流状态: {current_status}
    用户消息: {user_input}
    
    消息类型分类：
    - greeting（问候）：问候语
    - task（任务请求）：具体任务需求
    - supplement（补充信息）：针对当前任务的补充
    
    路由决策规则：
    - INITIALIZING/CLARIFYING_INTENT → intent_clarification
    - PLANNING → planning
    - EXECUTING → execution
    - REPORT → report
    """
```

#### IntentAnalysisAgent澄清提示词
```python
def build_clarification_prompt(user_input: str) -> str:
    return f"""
    你是专业的舆情分析助手，判断用户需求是否足够清晰。
    
    用户输入: "{user_input}"
    
    必需信息检查：
    - 具体分析对象（品牌/产品/人物）
    - 分析维度（声量/情感/传播等）
    - 时间范围（可选，默认近一个月）
    - 平台范围（可选，默认全平台）
    
    判断逻辑：
    intent_clear = true: 包含明确分析对象且能推断分析维度
    intent_clear = false: 缺少具体分析对象
    """
```

### 5.3 结构化输出

```python
# 使用Pydantic确保LLM输出格式
llm = configurable.get_supervisor_llm(temperature=0)
decision = llm.with_structured_output(RoutingDecision).invoke([HumanMessage(content=prompt)])

# 国际化消息管理
class MessageManager:
    def get_message(self, key: str, **kwargs) -> str:
        messages = MESSAGES.get(self.language, MESSAGES[DEFAULT_LANGUAGE])
        # 支持点分隔的嵌套键访问
        return self._get_nested_message(messages, key.split(".")).format(**kwargs)
```

---

## 6. 高级特性

### 6.1 多轮对话支持

#### 意图澄清多轮交互
```python
# 澄清轮次控制
clarification_round = state.get("clarification_round", 0)

if round > 0 and user_feedback_message:
    # 分析用户反馈意图
    feedback_analysis = llm.with_structured_output(UserFeedback).invoke([HumanMessage(content=prompt)])
    
    if feedback_analysis.intent_type == "agreement":
        # 用户同意，进入下一阶段
        return Command(goto="planning", update={"intent_approved": True})
    elif feedback_analysis.intent_type == "supplement":
        # 用户提供补充信息，重新分析
        supplement = feedback_analysis.extracted_info
        user_input = f"{user_input}\n\n补充信息：{supplement}"
```

#### 计划确认多轮交互
```python
# 计划修改支持
planning_round = state.get("planning_round", 0)

if round > 0 and feedback_messages:
    feedback_analysis = llm.with_structured_output(UserFeedback).invoke([HumanMessage(content=prompt)])
    
    if feedback_analysis.intent_type == "agreement":
        return Command(goto="execution", update={"plan_approved": True})
    elif feedback_analysis.intent_type == "supplement":
        # 根据反馈修改计划
        supplement = feedback_analysis.extracted_info
        user_input = f"{user_input}\n\n修改要求：{supplement}"
```

### 6.2 暂停恢复机制

#### 状态持久化
```python
from langgraph.checkpoint.memory import MemorySaver

# 创建检查点保存器
checkpointer = MemorySaver()

# 编译工作流时指定检查点
workflow = workflow_builder.compile(checkpointer=checkpointer)

# 会话状态管理
class BrandEventAPI:
    def __init__(self):
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
    
    async def chat(self, request: ChatRequest) -> ChatResponse:
        session_id = request.session_id or str(uuid.uuid4())
        
        if session_id not in self.active_sessions:
            # 创建新会话
            initial_state = create_initial_state(session_id=session_id, user_input=request.message)
        else:
            # 恢复现有会话
            config = {"configurable": {"thread_id": session_id}}
            current_state = self.workflow.workflow.get_state(config)
```

#### 状态恢复
```python
# 状态恢复逻辑
def restore_session_state(session_id: str) -> WorkflowState:
    config = {"configurable": {"thread_id": session_id}}
    current_state = workflow.get_state(config)
    
    if current_state:
        return current_state.values
    else:
        # 创建新的初始状态
        return create_initial_state(session_id=session_id)
```

### 6.3 流式处理

#### 实时状态更新
```python
async def _process_workflow_stream(self, input_data, config, state_for_events):
    """处理工作流流式消息"""
    async for chunk in self.workflow.workflow.astream(input_data, config=config, stream_mode="custom"):
        if "live_status_message" in chunk:
            # 实时状态更新
            self.event_handler.send_live_status(chunk['live_status_message'])
        elif "agent_message" in chunk:
            # 智能体消息
            self.event_handler.send_agent_message(chunk['agent_message'])
        elif "human_feedback_message" in chunk:
            # 等待用户反馈
            self.event_handler.send_agent_status(
                status="idle",
                brief="等待用户确认",
                description=chunk['human_feedback_message']
            )
```

#### 流式API端点
```python
@app.post("/chat/stream")
async def chat_stream(request: ChatRequest):
    """流式聊天端点"""
    return StreamingResponse(
        _stream_chat_response(api, request),
        media_type="text/plain"
    )

async def _stream_chat_response(api: BrandEventAPI, request: ChatRequest):
    """流式响应生成器"""
    try:
        session_id = request.session_id or "stream_session"
        
        # 发送初始状态
        yield f"data: {json.dumps({'type': 'status', 'message': '开始处理请求...'})}\n\n"
        
        # 处理请求并流式返回
        response = await api.chat(request)
        yield f"data: {json.dumps({'type': 'response', 'data': response.dict()})}\n\n"
        
        # 完成信号
        yield f"data:
{json.dumps({'type': 'complete'})}\n\n"
        
    except Exception as e:
        yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"
```

---

## 7. 技术亮点

### 7.1 核心技术特色

#### 状态驱动的确定性路由
- **9种工作流状态**: 精确控制业务流程
- **基于状态的路由决策**: 确保流程的确定性和可预测性
- **状态持久化**: 支持会话恢复和断点续传

#### 多智能体协作架构
- **分层协作模式**: 协调层、分析层、执行层清晰分工
- **专业化智能体**: 每个智能体专注特定领域，提高效率
- **统一异常处理**: 装饰器模式确保系统稳定性

#### 结构化LLM输出
- **Pydantic模型约束**: 确保LLM输出格式一致性
- **类型安全**: 编译时类型检查，减少运行时错误
- **自动验证**: 输入输出自动验证，提高系统可靠性

### 7.2 设计模式应用

#### 装饰器模式
```python
@node_exception_handler("NodeName")
async def node_function(state, writer, config):
    # 统一异常处理
    pass
```

#### 状态模式
```python
class WorkflowStatus(str, Enum):
    INITIALIZING = "initializing"
    CLARIFYING_INTENT = "clarifying_intent"
    PLANNING = "planning"
    EXECUTING = "executing"
    REPORT = "report"
    COMPLETED = "completed"
    FAILED = "failed"
```

#### 观察者模式
```python
# 流式消息处理
async for chunk in workflow.astream():
    if "live_status_message" in chunk:
        event_handler.send_live_status(chunk['live_status_message'])
```

### 7.3 工程实践

#### 错误处理
- **统一异常处理**: 装饰器模式统一处理节点异常
- **优雅降级**: 异常时返回有意义的错误信息
- **详细日志**: 结构化日志记录便于问题排查

#### 性能优化
- **流式处理**: 实时状态更新提升用户体验
- **异步编程**: 全面使用async/await提高并发性能
- **资源管理**: 及时清理临时文件和连接

#### 可维护性
- **模块化设计**: 清晰的分层架构便于维护
- **配置外部化**: 环境配置与代码分离
- **类型注解**: 完整的类型注解提高代码可读性

### 7.4 技术创新点

#### 智能意图澄清机制
```python
# 多轮澄清支持
if feedback_analysis.intent_type == "supplement":
    supplement = feedback_analysis.extracted_info
    user_input = f"{user_input}\n\n补充信息：{supplement}"
    # 重新进行意图分析
```

#### 动态计划生成与调整
```python
# 计划修改支持
if feedback_analysis.intent_type == "supplement":
    modification = feedback_analysis.extracted_info
    # 根据用户反馈调整执行计划
    updated_plan = generate_updated_plan(original_plan, modification)
```

#### 实时流式交互
```python
# 多类型消息流式传输
writer(status_msg("supervisor.analyzing_state"))      # 状态消息
writer(agent_msg("intent.analyzing_requirements"))    # 智能体消息
writer(feedback_msg("feedback.intent.need_approval")) # 反馈消息
```

---

## 📊 系统特性总结

### 核心能力
- **智能体数量**: 5个专业化智能体
- **工作流状态**: 9种状态精确控制
- **多轮交互**: 支持意图澄清和计划确认
- **暂停恢复**: 基于LangGraph的状态持久化
- **流式响应**: 实时状态更新优化体验

### 技术优势
- **架构先进**: 基于最新LangGraph框架
- **扩展性强**: 模块化设计支持功能扩展
- **稳定性高**: 完善的异常处理机制
- **用户友好**: 智能澄清需求，流式实时反馈

### 工程质量
- **代码规模**: 约3000+行核心代码
- **类型安全**: 完整的类型注解
- **测试覆盖**: 支持单元测试和集成测试
- **部署简单**: 容器化部署，支持云原生

---

## 🎯 总结

Brand-Event Agent 通过状态驱动的多智能体协作架构，实现了复杂舆情分析任务的智能化处理。系统在技术创新、工程实践和用户体验方面都达到了较高水准，为AI应用系统的设计和实现提供了优秀的参考范例。

**核心价值**:
1. **技术创新**: 状态驱动的多智能体协作模式
2. **业务专业**: 专门针对品牌舆情分析优化
3. **用户友好**: 多轮交互和实时反馈机制
4. **工程优秀**: 完善的错误处理和状态管理

---

*文档版本: v1.0 (精简版)*  
*最后更新: 2025年6月24日*  
*核心技术: LangGraph + 多智能体 + 状态驱动*