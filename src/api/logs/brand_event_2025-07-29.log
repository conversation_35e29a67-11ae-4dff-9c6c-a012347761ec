2025-07-29 15:28:33 - root - INFO - 日志配置完成 - 目录: logs, 级别: INFO
2025-07-29 15:28:33 - brand_event.api - INFO - [API] Brand Event API initialized successfully
2025-07-29 15:28:33 - brand_event.api - INFO - [API] Brand Event API initialized successfully
2025-07-29 15:30:14 - root - INFO - 日志配置完成 - 目录: logs, 级别: INFO
2025-07-29 15:30:14 - brand_event.api - INFO - [API] Brand Event API initialized successfully
2025-07-29 15:30:14 - brand_event.api - INFO - [API] Brand Event API initialized successfully
2025-07-29 15:30:28 - brand_event.api - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [API] Processing chat request
2025-07-29 15:30:28 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] Start doing workflow supervision
2025-07-29 15:30:36 - httpx - INFO - HTTP Request: POST https://llm-model-proxy.dev.fc.chj.cloud/agentops/chat/completions "HTTP/1.1 200 OK"
2025-07-29 15:30:36 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] User message analysis: type=task
2025-07-29 15:30:36 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] LLM routing decision: intent_clarification, reason: 用户提出了具体的任务请求，当前工作流状态为INITIALIZING，因此需要进行意图澄清。
2025-07-29 15:30:36 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] Completed workflow supervision, result: route to intent_clarification, reason: 用户提出了具体的任务请求，当前工作流状态为INITIALIZING，因此需要进行意图澄清。
2025-07-29 15:30:36 - brand_event.api - ERROR - [Session:2fj4oVeWNyIsUx4omPtha1] [API] Error processing chat request: 'ManusEventHandler' object has no attribute 'event_manager'
Traceback (most recent call last):
  File "/Users/<USER>/Code/AICODE/brand_event_agent/src/api/api.py", line 160, in chat
    final_state = await self._process_workflow_stream(initial_state, config, initial_state)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Code/AICODE/brand_event_agent/src/api/api.py", line 226, in _process_workflow_stream
    self.event_handler.send_live_status(chunk['live_status_message'], state=state_for_events)
  File "/Users/<USER>/Code/AICODE/brand_event_agent/src/messages/event_handler.py", line 175, in send_live_status
    if self.event_manager is None:
       ^^^^^^^^^^^^^^^^^^
AttributeError: 'ManusEventHandler' object has no attribute 'event_manager'
2025-07-29 15:31:04 - root - INFO - 日志配置完成 - 目录: logs, 级别: INFO
2025-07-29 15:31:04 - brand_event.api - INFO - [API] Brand Event API initialized successfully
2025-07-29 15:31:04 - brand_event.api - INFO - [API] Brand Event API initialized successfully
2025-07-29 15:31:07 - brand_event.api - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [API] Processing chat request
2025-07-29 15:31:07 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] Start doing workflow supervision
2025-07-29 15:31:11 - httpx - INFO - HTTP Request: POST https://llm-model-proxy.dev.fc.chj.cloud/agentops/chat/completions "HTTP/1.1 200 OK"
2025-07-29 15:31:11 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] User message analysis: type=task
2025-07-29 15:31:11 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] LLM routing decision: intent_clarification, reason: 用户提出了具体的任务请求，当前工作流状态为INITIALIZING，因此需要进行意图澄清。
2025-07-29 15:31:11 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] Completed workflow supervision, result: route to intent_clarification, reason: 用户提出了具体的任务请求，当前工作流状态为INITIALIZING，因此需要进行意图澄清。
2025-07-29 15:31:11 - agentops_event_sdk_python.subscribers.standard - INFO - Init SyncWebhookSubscriber: https://xuanji.chehejia.com/api/v1/agent-events, timeout: 10s, retries: 3
2025-07-29 15:31:11 - src.messages.event_handler - INFO - 事件管理器已初始化，Webhook URL: https://xuanji.chehejia.com/api/v1/agent-events, 控制台输出: True
2025-07-29 15:31:11 - agentops_event_sdk_python.subscribers.standard - INFO - Event[55af3a9b-f5cc-4300-9524-b65620456c7c] sent in 0.06s
2025-07-29 15:31:11 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在分析当前状态和用户消息...
2025-07-29 15:31:11 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在分析当前状态和用户消息...... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: 1lkeLZQ21EgVmL0Dgu9pyX, task_id: 5Lm16HoaaUHs1WJpYZYvxs
2025-07-29 15:31:11 - agentops_event_sdk_python.subscribers.standard - INFO - Event[764001dd-004d-4760-aa96-9877dde68ed6] sent in 0.12s
2025-07-29 15:31:11 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在分析用户消息类型和意图...
2025-07-29 15:31:11 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在分析用户消息类型和意图...... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: 1lkeLZQ21EgVmL0Dgu9pyX, task_id: 5Lm16HoaaUHs1WJpYZYvxs
2025-07-29 15:31:11 - brand_event.intentclarificationnode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [IntentClarificationNode] Start doing intent clarification (round 1)
2025-07-29 15:31:13 - httpx - INFO - HTTP Request: POST https://llm-model-proxy.dev.fc.chj.cloud/agentops/chat/completions "HTTP/1.1 200 OK"
2025-07-29 15:31:13 - brand_event.intentclarificationnode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [IntentClarificationNode] Completed intent clarification, result: intent is clear, waiting for approval
2025-07-29 15:31:13 - agentops_event_sdk_python.subscribers.standard - INFO - Event[b3ff93fd-f8b1-488c-9053-e28b470e39cb] sent in 0.02s
2025-07-29 15:31:13 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.CHAT> 💬 Assistant: 正在分析您的需求，确保完全理解您的需求。...
2025-07-29 15:31:13 - src.messages.event_handler - INFO - 发送Assistant ChatEvent: 正在分析您的需求，确保完全理解您的需求。... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: 1lkeLZQ21EgVmL0Dgu9pyX, task_id: 5Lm16HoaaUHs1WJpYZYvxs
2025-07-29 15:31:13 - agentops_event_sdk_python.subscribers.standard - INFO - Event[60866e40-e1d6-4219-9695-920229c70aea] sent in 0.03s
2025-07-29 15:31:13 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在分析用户意图...
2025-07-29 15:31:13 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在分析用户意图...... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: 1lkeLZQ21EgVmL0Dgu9pyX, task_id: 5Lm16HoaaUHs1WJpYZYvxs
2025-07-29 15:31:13 - agentops_event_sdk_python.subscribers.standard - INFO - Event[e9c59452-c5f8-4089-8611-154ca1faba34] sent in 0.13s
2025-07-29 15:31:13 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.CHAT> 💬 Assistant: 分析到您的真实需求为：
- 查询事件：理想L9在小红书的口碑趋势
- 时间范围：近一周
- 平台范围：小红书

请确认这个理解是否正确，我将据此为您制定详细的分析计划。...
2025-07-29 15:31:13 - src.messages.event_handler - INFO - 发送Assistant ChatEvent: 分析到您的真实需求为：
- 查询事件：理想L9在小红书的口碑趋势
- 时间范围：近一周
- 平台范围... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: 1lkeLZQ21EgVmL0Dgu9pyX, task_id: 5Lm16HoaaUHs1WJpYZYvxs
2025-07-29 15:31:13 - agentops_event_sdk_python.subscribers.standard - INFO - Event[ec3f0b58-aaa2-4f55-b149-688d520bc8aa] sent in 0.03s
2025-07-29 15:31:13 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.STATUS_UPDATE> 📄 Event: AgentStatusEvent
2025-07-29 15:31:13 - src.messages.event_handler - INFO - 发送AgentStatusEvent: idle - 等待用户确认 | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: 1lkeLZQ21EgVmL0Dgu9pyX, task_id: 5Lm16HoaaUHs1WJpYZYvxs
2025-07-29 15:31:13 - brand_event.api - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [API] Chat request completed successfully
2025-07-29 15:31:27 - brand_event.api - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [API] Processing chat request
2025-07-29 15:31:27 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] Start doing workflow supervision
2025-07-29 15:31:31 - httpx - INFO - HTTP Request: POST https://llm-model-proxy.dev.fc.chj.cloud/agentops/chat/completions "HTTP/1.1 200 OK"
2025-07-29 15:31:31 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] User message analysis: type=task
2025-07-29 15:31:31 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] LLM routing decision: intent_clarification, reason: 用户请求了具体的任务，当前工作流状态为CLARIFYING_INTENT，因此需要进行意图澄清。
2025-07-29 15:31:31 - brand_event.supervisornode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [SupervisorNode] Completed workflow supervision, result: route to intent_clarification, reason: 用户请求了具体的任务，当前工作流状态为CLARIFYING_INTENT，因此需要进行意图澄清。
2025-07-29 15:31:31 - agentops_event_sdk_python.subscribers.standard - INFO - Event[5afa1bd1-5b31-4831-86ff-965fa2d99775] sent in 0.08s
2025-07-29 15:31:31 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在分析当前状态和用户消息...
2025-07-29 15:31:31 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在分析当前状态和用户消息...... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: 1lkeLZQ21EgVmL0Dgu9pyX, task_id: 5Lm16HoaaUHs1WJpYZYvxs
2025-07-29 15:31:31 - agentops_event_sdk_python.subscribers.standard - INFO - Event[cbdd1f27-3b38-49dc-8a97-24f093a42874] sent in 0.03s
2025-07-29 15:31:31 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在分析用户消息类型和意图...
2025-07-29 15:31:31 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在分析用户消息类型和意图...... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: 1lkeLZQ21EgVmL0Dgu9pyX, task_id: 5Lm16HoaaUHs1WJpYZYvxs
2025-07-29 15:31:31 - brand_event.intentclarificationnode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [IntentClarificationNode] Start doing intent clarification (round 2)
2025-07-29 15:31:33 - httpx - INFO - HTTP Request: POST https://llm-model-proxy.dev.fc.chj.cloud/agentops/chat/completions "HTTP/1.1 200 OK"
2025-07-29 15:31:33 - brand_event.intentclarificationnode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [IntentClarificationNode] User feedback received, re-analyzing requirements
2025-07-29 15:31:36 - httpx - INFO - HTTP Request: POST https://llm-model-proxy.dev.fc.chj.cloud/agentops/chat/completions "HTTP/1.1 200 OK"
2025-07-29 15:31:36 - brand_event.intentclarificationnode - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [IntentClarificationNode] Completed intent clarification, result: intent is clear, waiting for approval
2025-07-29 15:31:36 - agentops_event_sdk_python.subscribers.standard - INFO - Event[a7a9b499-efd7-4bc9-8ca1-894b49aae22f] sent in 0.03s
2025-07-29 15:31:36 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在分析您的回复意图...
2025-07-29 15:31:36 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在分析您的回复意图...... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: 1lkeLZQ21EgVmL0Dgu9pyX, task_id: 5Lm16HoaaUHs1WJpYZYvxs
2025-07-29 15:31:36 - agentops_event_sdk_python.subscribers.standard - INFO - Event[9ba02aa6-1d8c-425d-bc62-15da2224461e] sent in 0.03s
2025-07-29 15:31:36 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.CHAT> 💬 Assistant: 我理解您提供的补充信息，让我重新分析您的需求。...
2025-07-29 15:31:36 - src.messages.event_handler - INFO - 发送Assistant ChatEvent: 我理解您提供的补充信息，让我重新分析您的需求。... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: 1lkeLZQ21EgVmL0Dgu9pyX, task_id: 5Lm16HoaaUHs1WJpYZYvxs
2025-07-29 15:31:36 - agentops_event_sdk_python.subscribers.standard - INFO - Event[0b0d1fb4-b896-419f-bbb6-43220cecd219] sent in 0.03s
2025-07-29 15:31:36 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.CHAT> 💬 Assistant: 分析到您的真实需求为：
- 查询事件：理想L9在小红书的口碑趋势
- 时间范围：近一周
- 平台范围：小红书

请确认这个理解是否正确，我将据此为您制定详细的分析计划。...
2025-07-29 15:31:36 - src.messages.event_handler - INFO - 发送Assistant ChatEvent: 分析到您的真实需求为：
- 查询事件：理想L9在小红书的口碑趋势
- 时间范围：近一周
- 平台范围... | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: 1lkeLZQ21EgVmL0Dgu9pyX, task_id: 5Lm16HoaaUHs1WJpYZYvxs
2025-07-29 15:31:36 - agentops_event_sdk_python.subscribers.standard - INFO - Event[2460dba6-1607-4b75-ac79-862c288a3f79] sent in 0.03s
2025-07-29 15:31:36 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.STATUS_UPDATE> 📄 Event: AgentStatusEvent
2025-07-29 15:31:36 - src.messages.event_handler - INFO - 发送AgentStatusEvent: idle - 等待用户确认 | session_id: 2fj4oVeWNyIsUx4omPtha1, sandbox_id: 1lkeLZQ21EgVmL0Dgu9pyX, task_id: 5Lm16HoaaUHs1WJpYZYvxs
2025-07-29 15:31:36 - brand_event.api - INFO - [Session:2fj4oVeWNyIsUx4omPtha1] [API] Chat request completed successfully
