# 品牌事件Agent技术总结文档

## 📋 目录

1. [系统概览](#1-系统概览)
2. [系统架构](#2-系统架构)
3. [智能体架构](#3-智能体架构)
4. [工作流引擎](#4-工作流引擎)
5. [执行流程分析](#5-执行流程分析)
6. [状态管理系统](#6-状态管理系统)
7. [提示词工程](#7-提示词工程)
8. [API架构设计](#8-api架构设计)
9. [工具集成](#9-工具集成)
10. [配置管理](#10-配置管理)
11. [日志监控](#11-日志监控)
12. [部署运维](#12-部署运维)
13. [技术亮点总结](#13-技术亮点总结)

---

## 1. 系统概览

### 1.1 项目定位

Brand-Event Agent 是一个基于 LangGraph 构建的**多智能体舆情分析系统**，专门为品牌舆情监控和分析而设计。系统采用状态驱动的工作流架构，通过多个专业化智能体协作完成复杂的舆情分析任务。

### 1.2 核心特性

- **多智能体协作**: 5个专业化智能体分工协作
- **状态驱动工作流**: 基于LangGraph的确定性路由机制
- **智能意图分析**: 自动理解和澄清用户需求
- **MCP工具集成**: 标准化的外部工具接入
- **流式响应**: 实时状态更新和用户体验优化
- **多轮交互**: 支持复杂的用户确认和反馈流程

### 1.3 技术选型

```mermaid
graph TB
    A[技术栈] --> B[核心框架]
    A --> C[智能体技术]
    A --> D[API服务]
    A --> E[数据处理]
    
    B --> B1[LangGraph 0.2.0+]
    B --> B2[LangChain 0.3.0+]
    B --> B3[LangChain-OpenAI]
    
    C --> C1[BaseAgent基类]
    C --> C2[Structured Output]
    C --> C3[MCP Protocol]
    
    D --> D1[FastAPI 0.104.0+]
    D --> D2[Uvicorn]
    D --> D3[Pydantic 2.5.0+]
    
    E --> E1[JWT认证]
    E --> E2[Structlog日志]
    E --> E3[HTTPx客户端]
```

---

## 2. 系统架构

### 2.1 整体架构设计

```mermaid
graph TB
    subgraph "用户层"
        U[用户请求]
    end
    
    subgraph "API网关层"
        API[FastAPI Gateway]
    end
    
    subgraph "工作流引擎与智能体层"
        WF[LangGraph Workflow]
        SM[状态管理器]
        SA[SupervisorAgent]
        IA[IntentAnalysisAgent]
        PA[PlanningAgent]
        EA[ExecutionAgent]
        RA[ReportAgent]
    end
    
    subgraph "工具集成层"
        MCP[MCP客户端]
        TOOLS[外部工具]
        RPT[报告服务]
    end
    
    subgraph "数据存储层"
        MEM[内存存储]
        FILE[文件存储]
    end
    
    U --> API
    API --> WF
    WF --> SM
    WF --> SA
    SA --> IA
    SA --> PA
    SA --> EA
    SA --> RA
    EA --> MCP
    MCP --> TOOLS
    RA --> RPT
    SM --> MEM
    RA --> FILE
```

### 2.2 模块化设计

项目采用清晰的模块化设计：

```
src/
├── api/                 # API服务层
│   ├── __init__.py     # API模块导出
│   ├── api.py          # 核心API实现
│   └── app.py          # FastAPI应用配置
├── core/               # 核心业务层
│   ├── config.py       # 配置管理
│   ├── prompts.py      # 提示词管理
│   ├── state.py        # 状态定义
│   └── workflow.py     # 工作流实现
├── messages/           # 消息处理层
│   ├── event_handler.py # 事件处理器
│   └── messages.py     # 国际化消息
├── utils/              # 工具函数层
│   ├── log_config.py   # 日志配置
│   ├── logger.py       # 日志工具
│   └── utils.py        # 通用工具
└── tests/              # 测试代码
```

### 2.3 依赖关系图

```mermaid
graph LR
    API[api层] --> CORE[core层]
    API --> MSG[messages层]
    API --> UTILS[utils层]
    
    CORE --> UTILS
    MSG --> UTILS
    
    WF[workflow.py] --> STATE[state.py]
    WF --> CONFIG[config.py]
    WF --> PROMPTS[prompts.py]
    WF --> UTILS
```

---

## 3. 智能体架构

### 3.1 多智能体协作模式

系统采用**分层协作模式**，每个智能体都有明确的职责分工：

```mermaid
graph TD
    subgraph "协调层"
        SUP[SupervisorAgent<br/>协调路由智能体]
    end
    
    subgraph "分析层"
        INT[IntentAnalysisAgent<br/>意图分析智能体]
        PLAN[PlanningAgent<br/>计划制定智能体]
    end
    
    subgraph "执行层"
        EXEC[ExecutionAgent<br/>任务执行智能体]
        REP[ReportAgent<br/>报告生成智能体]
    end
    
    SUP --> INT
    SUP --> PLAN
    SUP --> EXEC
    SUP --> REP
    
    INT --> PLAN
    PLAN --> EXEC
    EXEC --> REP
```

### 3.2 智能体职责分工

#### 3.2.1 SupervisorAgent (协调路由)

**核心职责**: 统一消息分析和智能路由决策

**关键功能**:
- 分析用户消息类型（greeting/task/supplement）
- 基于业务状态进行路由决策
- 协调各智能体间的工作流转

**实现特点**:
```python
# 路由决策逻辑
class RoutingDecision(BaseModel):
    message_analysis: UserMessageType
intent_clarification", "planning", "execution", "report", "__end__"]
    reason: str
    response_message: str
    workflow_status: str

# 路由规则
ROUTING_RULES = {
    "INITIALIZING": "intent_clarification",
    "CLARIFYING_INTENT": "intent_clarification", 
    "PLANNING": "planning",
    "EXECUTING": "execution",
    "REPORT": "report"
}
```

#### 3.2.2 IntentAnalysisAgent (意图分析)

**核心职责**: 理解和澄清用户需求

**关键功能**:
- 判断用户需求是否足够清晰
- 生成针对性澄清问题
- 分析用户回复意图（同意/补充/拒绝）
- 结构化需求信息

**澄清逻辑**:
```python
# 必需信息检查
required_info = {
    "analysis_object": "具体品牌/产品/人物",
    "analysis_dimension": "声量/情感/传播/口碑等",
    "time_range": "时间范围（可选，默认近一个月）",
    "platform_range": "平台范围（可选，默认全平台）"
}

class ClarificationResult(BaseModel):
    intent_clear: bool
    clarification_questions: List[str]
    clarification_result: Optional[Dict[str, Any]]
    summary: str
    response_message: str
```

#### 3.2.3 PlanningAgent (计划制定)

**核心职责**: 制定详细的执行计划

**关键功能**:
- 基于澄清后的需求生成执行计划
- 将复杂任务分解为具体步骤
- 支持计划修改和优化
- 提供降级计划机制

**计划结构**:
```python
class PlanStep(BaseModel):
    title: str
    description: str
    estimated_time: str = ""
    skip_execute: bool = False

class Plan(BaseModel):
    title: str
    objective: str
    steps: List[PlanStep]
```

#### 3.2.4 ExecutionAgent (任务执行)

**核心职责**: 执行具体的分析任务

**关键功能**:
- 遍历执行计划中的每个步骤
- 集成MCP (Model Context Protocol) 工具
- 动态生成JWT访问令牌
- 详细的执行日志和调试信息

**MCP集成**:
```python
# 动态JWT令牌生成
def create_access_token(user_id: str) -> str:
    payload = {
        "sub": user_id,
        "exp": datetime.utcnow() + timedelta(minutes=2880),  # 48小时
        "iat": datetime.utcnow(),
        "type": "access"
    }
    return jwt.encode(payload, "brand_event", algorithm="HS256")

# MCP工具调用
server_configs = {
    "brand_event": {
        "transport": "streamable_http",
        "url": "http://************:5003/mcp/marketing",
        "headers": {
            "sign": create_access_token(user_id)  # 动态令牌
        }
    }
}
```

#### 3.2.5 ReportAgent (报告生成)

**核心职责**: 生成最终分析报告

**关键功能**:
- 整合执行结果
- 生成结构化报告
- 支持多种输出格式
- 报告质量验证

---

## 4. 工作流引擎

### 4.1 LangGraph工作流设计

系统基于LangGraph构建状态驱动的工作流引擎：

```python
def create_workflow_graph() -> StateGraph:
    """创建品牌事件分析工作流图"""
    builder = StateGraph(
        WorkflowState,
        config_schema=WorkflowConfiguration
    )
    
    # 添加所有工作流节点
    builder.add_node("supervisor", supervisor_node)
    builder.add_node("intent_clarification", intent_clarification_node)
    builder.add_node("planning", planning_node)
    builder.add_node("execution", execution_node)
    builder.add_node("report", report_node)
    
    # 设置入口点
    builder.set_entry_point("supervisor")
    
    return builder
```

### 4.2 节点异常处理

统一的异常处理机制确保系统稳定性：

```python
def node_exception_handler(node_name: str):
    """节点异常处理装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(state: WorkflowState, writer: StreamWriter, config: RunnableConfig) -> Command:
            try:
                return await func(state, writer, config)
            except Exception as e:
                logger.error(f"{node_name} failed: {str(e)}", session_id=session_id, exc_info=True)
                return Command(
                    goto="__end__",
                    update={
                        "workflow_status": WorkflowStatus.FAILED,
                        "error_info": {"message": str(e), "node": node_name.lower()}
                    }
                )
        return wrapper
    return decorator
```

### 4.3 流式处理机制

支持实时状态更新的流式处理：

```python
async def _process_workflow_stream(self, input_data, config, state_for_events):
    """处理工作流流式消息"""
    async for chunk in self.workflow.workflow.astream(input_data, config=config, stream_mode="custom"):
        if "live_status_message" in chunk:
            self.event_handler.send_live_status(chunk['live_status_message'], state=state_for_events)
        elif "agent_message" in chunk:
            self.event_handler.send_agent_message(state_for_events, chunk['agent_message'])
        elif "human_feedback_message" in chunk:
            self.event_handler.send_agent_status(
                status="idle",
                brief="等待用户确认",
                description=chunk['human_feedback_message'],
                state=state_for_events
            )
```

---

## 5. 执行流程分析

### 5.1 正常执行流程

```mermaid
graph TD
    A[用户输入] --> B[Supervisor路由]
    B --> C[意图分析]
    C --> D{意图是否清晰?}
    D -->|否| E[澄清问题]
    E --> F[等待用户回复]
    F --> C
    D -->|是| G[制定计划]
    G --> H{计划是否确认?}
    H -->|否| I[修改计划]
    I --> G
    H -->|是| J[执行任务]
    J --> K[生成报告]
    K --> L[完成]
```

### 5.2 反复澄清流程

系统支持多轮意图澄清：

```python
# 澄清轮次控制
clarification_round = state.get("clarification_round", 0)
max_clarification_rounds = 3

if round > 0 and user_feedback_message:
    feedback_analysis = llm.with_structured_
    next: Literal["
output(UserFeedback).invoke([HumanMessage(content=prompt)])
    
    if feedback_analysis.intent_type == "agreement":
        # 用户同意，继续下一步
        return Command(goto="planning", update={"intent_approved": True})
    elif feedback_analysis.intent_type == "supplement":
        # 用户提供补充信息，重新分析
        supplement = feedback_analysis.extracted_info
        user_input = f"{user_input}\n\n补充信息：{supplement}"
```

### 5.3 异常处理流程

```mermaid
graph TD
    A[节点执行] --> B{是否发生异常?}
    B -->|否| C[正常返回]
    B -->|是| D[异常捕获]
    D --> E[记录错误日志]
    E --> F[更新状态为FAILED]
    F --> G[返回错误信息]
    G --> H[工作流结束]
```

---

## 6. 状态管理系统

### 6.1 状态模型设计

系统使用TypedDict定义完整的状态模型：

```python
class WorkflowStatus(str, Enum):
    """工作流状态枚举"""
    INITIALIZING = "initializing"
    CLARIFYING_INTENT = "clarifying_intent"
    PLANNING = "planning"
    EXECUTING = "executing"
    SUMMARIZING = "summarizing"
    REPORT = "report"
    COMPLETED = "completed"
    FAILED = "failed"

class WorkflowState(TypedDict):
    """工作流状态定义"""
    # 核心工作流状态
    messages: Annotated[List[BaseMessage], add_messages]
    session_id: str
    user_id: Optional[str]
    user_input: str
    
    # 工作流控制
    workflow_status: WorkflowStatus
    current_step: str
    requires_human_approval: bool
    
    # 意图分析
    intent_clarified: bool
    intent_approved: bool
    intent_summary: str
    clarification_result: Optional[Dict[str, Any]]
    clarification_round: Optional[int]
    
    # 计划制定
    task_plan: Optional[Dict[str, Any]]
    plan_approved: bool
    planning_round: Optional[int]
    
    # 执行和报告
    execution_started: bool
    execution_results: List[Dict[str, Any]]
    execution_report: Optional[str]
    final_report: Optional[str]
    
    # 错误处理
    error_info: Optional[Dict[str, Any]]
```

### 6.2 状态生命周期

```mermaid
stateDiagram-v2
    [*] --> INITIALIZING
    INITIALIZING --> CLARIFYING_INTENT
    CLARIFYING_INTENT --> CLARIFYING_INTENT : 需要更多澄清
    CLARIFYING_INTENT --> PLANNING : 意图明确
    PLANNING --> PLANNING : 计划需要修改
    PLANNING --> EXECUTING : 计划确认
    EXECUTING --> REPORT : 执行完成
    REPORT --> COMPLETED : 报告生成成功
    
    CLARIFYING_INTENT --> FAILED : 澄清失败
    PLANNING --> FAILED : 计划制定失败
    EXECUTING --> FAILED : 执行失败
    REPORT --> FAILED : 报告生成失败
    
    FAILED --> [*]
    COMPLETED --> [*]
```

### 6.3 状态持久化

使用LangGraph的MemorySaver进行状态持久化：

```python
from langgraph.checkpoint.memory import MemorySaver

# 创建检查点保存器
checkpointer = MemorySaver()

# 编译工作流时指定检查点
workflow = workflow_builder.compile(checkpointer=checkpointer)

# 状态恢复
config = {"configurable": {"thread_id": session_id}}
current_state = workflow.get_state(config)
```

---

## 7. 提示词工程

### 7.1 提示词设计原则

系统采用模块化的提示词设计：

- **角色定义明确**: 每个智能体都有清晰的角色定位
- **任务描述具体**: 详细说明智能体的具体职责
- **输出格式标准化**: 使用Pydantic模型确保结构化输出
- **上下文感知**: 根据当前状态动态构建提示词

### 7.2 结构化输出设计

```python
# SupervisorAgent的结构化输出
class RoutingDecision(BaseModel):
    message_analysis: UserMessageType = Field(description="用户消息分析结果")
    next: Literal["intent_clarification", "planning", "execution", "report", "__end__"] = Field(description="下一步路由目标")
    reason: str = Field(description="路由决策理由")
    response_message: str = Field(default="", description="返回给用户的消息")
    workflow_status: str = Field(description="更新后的工作流状态")

# 使用结构化输出
llm = configurable.get_supervisor_llm(temperature=0)
decision = llm.with_structured_output(RoutingDecision).invoke([HumanMessage(content=prompt)])
```

### 7.3 提示词模板系统

```python
# 意图澄清提示词模板
def build_clarification_prompt(user_input: str) -> str:
    current_time = datetime.now().strftime("%a %b %d %Y %H:%M:%S %z")
    return f"""
    ---
    CURRENT_TIME: {current_time}
    ---
    
    你是专业的舆情分析助手，需要判断用户的舆情分析需求是否足够清晰。
    
    ## 用户输入
    "{user_input}"
    
    ## 必需信息检查
    **核心要求：明确的分析事件**
    - 必须包含具体的品牌、产品、人物或话题名称
    - 需要明确或能推断出分析维度（如网络声量、观点分布等）
    
    ## 判断逻辑
    **intent_clear = true**：包含明确的分析对象且能推断分析维度
    **intent_clear = false**：缺少具体分析对象，只有模糊描述
    """
```

### 7.4 多语言支持

系统支持中英文双语：

```python
# 消息国际化系统
MESSAGES = {
    "zh_CN": {
        "supervisor": {
            "analyzing_state": "正在分析当前状态和用户消息...",
            "routing_error": "路由分析遇到问题，使用默认策略"
        }
    },
    "en_US": {
        "supervisor": {
            "analyzing_state": "Analyzing current state and user messages...",
            "routing_error": "Routing analysis encountered issues, using default strategy"
        }
    }
}

class MessageManager:
    def get_message(self, key: str, **kwargs) -> str:
        keys = key.split(".")
        messages = MESSAGES.get(self.language, MESSAGES[DEFAULT_LANGUAGE])
        current = messages
        for k in keys:
            current = current[k]
        return current.format(**kwargs) if isinstance(current, str)
else str(current)
```

---

## 8. API架构设计

### 8.1 RESTful API设计

系统提供完整的RESTful API接口：

```python
# 主要API端点
@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest) -> ChatResponse:
    """主要聊天端点 - 保持与现有API的兼容性"""

@app.post("/chat/stream")
async def chat_stream(request: ChatRequest):
    """流式聊天端点，用于实时更新"""

@app.get("/session/{session_id}", response_model=SessionInfo)
async def get_session(session_id: str) -> SessionInfo:
    """获取会话信息"""

@app.delete("/session/{session_id}")
async def delete_session(session_id: str):
    """删除会话及其数据"""

@app.get("/status", response_model=SystemStatus)
async def get_system_status() -> SystemStatus:
    """获取系统状态信息"""

@app.get("/health", response_model=HealthCheck)
async def health_check() -> HealthCheck:
    """健康检查端点"""
```

### 8.2 请求响应模型

```python
class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str = Field(..., description="用户发送的消息内容")
    session_id: Optional[str] = Field(None, description="会话ID")
    task_id: Optional[str] = Field(None, description="任务ID")
    sandbox_id: Optional[str] = Field(None, description="沙箱ID")
    event_webhook: Optional[str] = Field(None, description="上传事件回调地址")
    
    # 报表DSL字段
    report_dsl_data: Optional[Dict[str, Any]] = Field(None, description="报表DSL数据结构")
    report_dsl_status: Optional[Literal["SUCCESS", "FAILED"]] = Field(None, description="报表DSL生成状态")
    report_dsl_message: Optional[str] = Field(None, description="报表DSL状态消息")
    
    # 工作流状态字段
    workflow_status: Optional[WorkflowStatus] = Field(None, description="期望的工作流状态")

class ChatResponse(BaseModel):
    """聊天响应模型"""
    session_id: str = Field(..., description="会话ID")
    response: str = Field(..., description="系统响应")
    status: str = Field(..., description="当前工作流状态")
    requires_feedback: bool = Field(False, description="是否需要用户反馈")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="响应元数据")
```

### 8.3 流式响应机制

```python
async def _stream_chat_response(api: BrandEventAPI, request: ChatRequest) -> AsyncGenerator[str, None]:
    """流式聊天响应生成器"""
    try:
        session_id = request.session_id or "stream_session"
        
        # 发送初始状态
        yield f"data: {json.dumps({'type': 'status', 'message': '开始处理请求...', 'session_id': session_id})}\n\n"
        
        # 处理聊天请求
        response = await api.chat(request)
        
        # 发送响应数据
        yield f"data: {json.dumps({'type': 'response', 'data': response.dict()})}\n\n"
        
        # 发送完成信号
        yield f"data: {json.dumps({'type': 'complete', 'session_id': session_id})}\n\n"
        
    except Exception as e:
        yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"
```

### 8.4 会话管理

```python
class BrandEventAPI:
    def __init__(self):
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.total_sessions = 0
    
    async def chat(self, request: ChatRequest) -> ChatResponse:
        session_id = request.session_id or str(uuid.uuid4())
        
        if session_id not in self.active_sessions:
            # 创建新会话
            self.active_sessions[session_id] = {
                "created_at": datetime.now(),
                "user_id": request.user_id,
                "message_count": 1,
                "workflow_status": request.workflow_status,
            }
            self.total_sessions += 1
            
            # 创建初始状态
            initial_state = create_initial_state(
                session_id=session_id,
                user_input=request.message,
                user_id=request.user_id,
                # ... 其他参数
            )
        else:
            # 继续现有会话
            self.active_sessions[session_id]["message_count"] += 1
            # 更新会话状态
```

---

## 9. 工具集成

### 9.1 MCP协议集成

系统使用Model Context Protocol (MCP) 进行工具集成：

```python
from langchain_mcp_adapters.client import MultiServerMCPClient

# MCP服务器配置
def create_mcp_server_config(self, user_id: str, jwt_token: str) -> Dict[str, Any]:
    return {
        "brand_event": {
            "transport": "streamable_http",
            "url": "http://************:5003/mcp/marketing",
            "headers": {
                "account": user_id,
                "sign": jwt_token
            }
        }
    }

# MCP客户端初始化
mcp_client = MultiServerMCPClient(server_configs)
tools = await mcp_client.get_tools()

# 创建React Agent
from langgraph.prebuilt import create_react_agent
agent = create_react_agent(llm, tools)
```

### 9.2 外部服务集成

#### 9.2.1 报告生成服务

```python
def generate_html_report(
    dsl_data: Dict[str, Any],
    report_service_url: str,
    api_key: str,
    timeout: int = 300
) -> Dict[str, Any]:
    """从DSL数据生成HTML报告"""
    url = f"{report_service_url}/__ui/report"
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    payload = json.dumps(dsl_data, ensure_ascii=False)
    
    response = requests.post(url, headers=headers, data=payload, timeout=timeout)
    
    if response.status_code == 200:
        return {
            "success": True,
            "html_content": response.text,
            "message": "报告生成成功"
        }
    else:
        return {
            "success": False,
            "error": f"HTTP {response.status_code}: {response.text}",
            "message": "报告生成失败"
        }
```

#### 9.2.2 文件上传服务

```python
def upload_html_to_s3(
    html_content: str,
    filename: str,
    upload_url: str,
    timeout: int = 300
) -> Dict[str, Any]:
    """上传HTML内容到S3存储"""
try:
        # 创建临时文件
        temp_file_path = os.path.join(tempfile.gettempdir(), filename)
        
        with open(temp_file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        # 上传文件
        with open(temp_file_path, 'rb') as file:
            files = {'file': (filename, file, 'text/html')}
            response = requests.post(upload_url, files=files, timeout=timeout)
        
        if response.status_code == 200:
            return {
                "success": True,
                "upload_result": response.json(),
                "filename": filename,
                "message": "HTML报告上传成功"
            }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"HTML报告上传失败：{str(e)}"
        }
    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
```

### 9.3 认证授权机制

#### 9.3.1 JWT令牌生成

```python
import jwt
from datetime import datetime, timedelta

SECRET_KEY = "brand_event"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 2880  # 48小时

def create_access_token(user_id: str) -> str:
    """生成JWT访问令牌"""
    expire = datetime.now(datetime.timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    payload = {
        "sub": user_id,          # 用户ID
        "exp": expire,           # 过期时间
        "iat": datetime.now(datetime.timezone.utc),  # 签发时间
        "type": "access"         # 令牌类型
    }
    
    return jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
```

#### 9.3.2 动态认证

```python
# 在ExecutionAgent中动态生成令牌
user_id = state.get('user_id') or '<EMAIL>'
token = create_access_token(user_id)

# 配置MCP服务器认证头
server_configs = configurable.create_mcp_server_config(
    user_id=user_id,
    session_id=session_id,
    task_id=task_id,
    sandbox_id=sandbox_id,
    jwt_token=token
)
```

---

## 10. 配置管理

### 10.1 环境配置

系统使用dataclass进行配置管理：

```python
@dataclass(kw_only=True)
class WorkflowConfiguration:
    """工作流配置类"""
    
    # 模型配置
    supervisor_model: str = "azure-gpt-4o-mini"
    analyst_model: str = "azure-gpt-4o-mini"
    planner_model: str = "azure-gpt-4o-mini"
    executor_model: str = "azure-gpt-4o-mini"
    reporter_model: str = "azure-gpt-4o-mini"
    
    # API配置
    openai_api_key: str = "test"
    openai_base_url: str = "https://llm-model-proxy.dev.fc.chj.cloud/agentops"
    
    # 报告服务配置
    report_service_url: str = "https://console-playground.fed.chehejia.com"
    report_api_key: str
    upload_service_url: str = "https://xuanji-backend-service-dev.inner.chj.cloud/api/v1/ois/upload"
    report_timeout: int = 300
    
    # MCP配置
    mcp_server_url: str = "http://************:5003/mcp/marketing"
    mcp_timeout: int = 300
    
    # 工作流配置
    max_iterations: int = 10
    timeout_seconds: int = 300
    max_planning_rounds: int = 3
    max_clarification_rounds: int = 3
    
    # 安全配置
    jwt_secret_key: str = "brand_event"
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 2880  # 48小时
```

### 10.2 模型配置

```python
def create_llm(self, model_name: str, temperature: float = 0.0) -> ChatOpenAI:
    """创建ChatOpenAI实例"""
    return ChatOpenAI(
        base_url=self.openai_base_url,
        model=model_name,
        api_key=SecretStr(self.openai_api_key),
        temperature=temperature,
    )

# 各智能体专用LLM获取方法
def get_supervisor_llm(self, temperature: float = 0.0) -> ChatOpenAI:
    return self.create_llm(self.supervisor_model, temperature)

def get_analyst_llm(self, temperature: float = 0.0) -> ChatOpenAI:
    return self.create_llm(self.analyst_model, temperature)
```

### 10.3 服务配置

```python
def create_mcp_server_config(self, user_id: str, jwt_token: str) -> Dict[str, Any]:
    """创建MCP服务器配置"""
    headers = {"account": user_id}
    
    if jwt_token:
        headers["sign"] = jwt_token
    
    return {
        "brand_event": {
            "transport": "streamable_http",
            "url": self.mcp_server_url,
            "headers": headers
        }
    }
```

---

## 11. 日志监控

### 11.1 结构化日志

系统使用structlog进行结构化日志记录：

```python
import structlog
from src.utils.logger import get_workflow_logger, LogMessages

class LogMessages:
    """日志消息常量"""
    WORKFLOW_SUPERVISION_START = "开始工作流监督"
    WORKFLOW_SUPERVISION_COMPLETE = "工作流监督完成，目标: {destination}, 原因: {reason}"
    USER_MESSAGE_ANALYSIS = "用户消息分析: {message_type}"
    LLM_ROUTING_DECISION = "LLM路由决策: {next}, 原因: {reason}"
    WORKFLOW_INTENT_START = "开始意图分析，轮次: {round}"
    WORKFLOW_INTENT_COMPLETE = "意图分析完成: {result}"
    WORKFLOW_PLANNING_START = "开始计划制定，轮次: {round}"
    WORKFLOW_PLANNING_COMPLETE = "计划制定完成: {result}"
    WORKFLOW_EXECUTION_START = "开始执行，总步骤数: {total_steps}"
    WORKFLOW_EXECUTION_COMPLETE = "执行完成"
    WORKFLOW_REPORT_START = "开始生成报告"
    WORKFLOW_REPORT_COMPLETE = "报告生成完成"

def get_workflow_logger(name: str):
    """获取工作流日志记录器"""
    return structlog.get_logger(name)

# 使用示例
logger = get_workflow_logger("SupervisorNode")
logger.info(LogMessages.WORKFLOW_SUPERVISION_START, session_id=session_id)
```

### 11.
2 错误追踪

系统提供详细的错误追踪机制：

```python
@node_exception_handler("SupervisorNode")
async def supervisor_node(state: WorkflowState, writer: StreamWriter, config: RunnableConfig) -> Command:
    try:
        # 节点逻辑
        return await func(state, writer, config)
    except Exception as e:
        logger.error(f"SupervisorNode failed: {str(e)}", session_id=session_id, exc_info=True)
        writer(status_msg("supervisor.error"))
        return Command(
            goto="__end__",
            update={
                "workflow_status": WorkflowStatus.FAILED,
                "error_info": {
                    "message": str(e),
                    "node": "supervisor"
                }
            }
        )
```

### 11.3 性能监控

```python
import time
from datetime import datetime

class PerformanceMonitor:
    def __init__(self):
        self.start_time = datetime.now()
        self.metrics = {}
    
    def record_execution_time(self, node_name: str, execution_time: float):
        """记录节点执行时间"""
        if node_name not in self.metrics:
            self.metrics[node_name] = []
        self.metrics[node_name].append(execution_time)
    
    def get_average_execution_time(self, node_name: str) -> float:
        """获取节点平均执行时间"""
        if node_name in self.metrics:
            return sum(self.metrics[node_name]) / len(self.metrics[node_name])
        return 0.0

# 使用示例
start_time = time.time()
# 执行节点逻辑
execution_time = time.time() - start_time
monitor.record_execution_time("supervisor", execution_time)
```

---

## 12. 部署运维

### 12.1 容器化部署

系统支持Docker容器化部署：

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装依赖
COPY pyproject.toml uv.lock ./
RUN pip install uv && uv sync --frozen

# 复制源代码
COPY src/ ./src/
COPY docs/ ./docs/

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "uvicorn", "src.api.app:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 12.2 环境配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  brand-event-agent:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL}
      - REPORT_API_KEY=${REPORT_API_KEY}
      - MCP_SERVER_URL=${MCP_SERVER_URL}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
```

### 12.3 扩展性设计

系统设计支持水平扩展：

```python
# 多实例负载均衡
class BrandEventAPI:
    def __init__(self, instance_id: str = None):
        self.instance_id = instance_id or str(uuid.uuid4())
        self.start_time = datetime.now()
        
        # 分布式会话存储（可扩展为Redis等）
        self.session_store = self._init_session_store()
    
    def _init_session_store(self):
        """初始化会话存储（可配置为分布式存储）"""
        # 当前使用内存存储，可扩展为Redis、数据库等
        return {}
```

---

## 13. 技术亮点总结

### 13.1 核心技术特色

#### 13.1.1 状态驱动的确定性路由

- **9种工作流状态**: 精确控制业务流程
- **基于状态的路由决策**: 确保流程的确定性和可预测性
- **状态持久化**: 支持会话恢复和断点续传

#### 13.1.2 多智能体协作架构

- **分层协作模式**: 协调层、分析层、执行层清晰分工
- **专业化智能体**: 每个智能体专注特定领域，提高效率
- **统一异常处理**: 装饰器模式确保系统稳定性

#### 13.1.3 结构化LLM输出

- **Pydantic模型约束**: 确保LLM输出格式一致性
- **类型安全**: 编译时类型检查，减少运行时错误
- **自动验证**: 输入输出自动验证，提高系统可靠性

#### 13.1.4 MCP标准化工具集成

- **协议标准化**: 使用Model Context Protocol标准
- **动态工具加载**: 运行时动态获取和使用外部工具
- **认证机制**: JWT动态令牌确保安全性

### 13.2 设计模式应用

#### 13.2.1 装饰器模式

```python
@node_exception_handler("NodeName")
async def node_function(state, writer, config):
    # 节点逻辑
    pass
```

#### 13.2.2 策略模式

```python
# 不同智能体采用不同的处理策略
class AgentStrategy:
    def process(self, state: WorkflowState) -> Command:
        pass

class SupervisorStrategy(AgentStrategy):
    def process(self, state: WorkflowState) -> Command:
        # 路由逻辑
        pass
```

#### 13.2.3 观察者模式

```python
# 流式消息处理
async for chunk in workflow.astream():
    if "live_status_message" in chunk:
        event_handler.send_live_status(chunk['live_status_message'])
    elif "agent_message" in chunk:
        event_handler.send_agent_message(chunk['agent_message'])
```

### 13.3 最佳实践总结

#### 13.3.1 错误处理

- **统一异常处理**: 装饰器模式统一处理节点异常
- **优雅降级**: 异常时返回有意义的错误信息
- **详细日志**: 结构化日志记录便于问题排查

#### 13.3.2 性能优化

- **流式处理**: 实时状态更新提升用户体验
- **异步编程**: 全面使用async/await提高并发性能
- **资源管理**: 及时清理临时文件和连接

#### 13.3.3 可维护性

- **模块化设计**: 清晰的分层架构便于维护
- **配置外部化**: 环境配置与代码分离
- **类型注解**: 完整的类型注解提高代码可读性

#### 13.3.4 可扩展性

- **插件化架构**: MCP协议支持动态工具扩展
- **配置驱动**: 通过配置文件调整系统行为
- **接口标准
化**: RESTful API设计便于集成

### 13.4 技术创新点

#### 13.4.1 智能意图澄清机制

- **多轮澄清**: 支持复杂需求的逐步澄清
- **上下文理解**: 基于历史对话进行智能分析
- **结构化需求**: 自动提取和验证关键信息

#### 13.4.2 动态计划生成与调整

- **需求驱动**: 基于用户真实需求生成执行计划
- **计划优化**: 支持用户反馈的计划调整
- **步骤分解**: 复杂任务自动分解为可执行步骤

#### 13.4.3 实时流式交互

- **状态实时更新**: 用户可实时了解系统处理进度
- **多消息类型**: 支持状态消息、智能体消息、反馈消息等
- **用户体验优化**: 减少等待时间，提升交互体验

### 13.5 系统优势

#### 13.5.1 业务价值

- **专业化分析**: 专门针对品牌舆情分析场景设计
- **智能化程度高**: 自动理解需求、制定计划、执行分析
- **结果可视化**: 生成HTML可视化报告，直观展示分析结果

#### 13.5.2 技术价值

- **架构先进**: 基于最新的LangGraph框架构建
- **扩展性强**: 模块化设计支持功能扩展
- **稳定性高**: 完善的异常处理和日志监控

#### 13.5.3 工程价值

- **代码质量高**: 完整的类型注解和文档
- **测试覆盖**: 支持单元测试和集成测试
- **部署简单**: 容器化部署，支持云原生架构

---

## 📊 项目统计

### 代码规模
- **总代码行数**: 约3000+行
- **核心模块**: 5个智能体 + 1个工作流引擎
- **API接口**: 6个主要端点
- **配置项**: 20+个可配置参数

### 技术栈统计
- **核心框架**: LangGraph 0.2.0+, LangChain 0.3.0+
- **API框架**: FastAPI 0.104.0+
- **数据验证**: Pydantic 2.5.0+
- **日志系统**: Structlog
- **认证机制**: JWT
- **协议标准**: MCP (Model Context Protocol)

### 功能特性
- **智能体数量**: 5个专业化智能体
- **工作流状态**: 9种状态管理
- **支持语言**: 中文、英文
- **输出格式**: JSON、HTML、文本
- **部署方式**: Docker容器化

---

## 🎯 总结

Brand-Event Agent 是一个技术先进、架构合理、功能完善的多智能体舆情分析系统。通过状态驱动的工作流设计、专业化的智能体协作、标准化的工具集成和优秀的用户体验，为品牌舆情分析提供了完整的解决方案。

### 核心价值
1. **技术创新**: 基于LangGraph的状态驱动多智能体架构
2. **业务专业**: 专门针对品牌舆情分析场景优化
3. **用户友好**: 智能澄清需求，流式实时反馈
4. **扩展性强**: 模块化设计，支持功能和工具扩展
5. **工程质量**: 完善的错误处理、日志监控和部署方案

该系统不仅展示了多智能体协作的技术实力，更体现了在复杂业务场景下的工程实践能力，为类似的AI应用系统提供了优秀的参考范例。

---

*文档版本: v1.0*  
*最后更新: 2025年6月24日*  
*技术栈: LangGraph + LangChain + FastAPI + MCP*