2025-06-24 10:19:13 - root - INFO - 日志配置完成 - 目录: logs, 级别: INFO
2025-06-24 10:19:13 - brand_event.api - INFO - [API] Brand Event API initialized successfully
2025-06-24 10:19:13 - brand_event.api - INFO - [API] Brand Event API initialized successfully
2025-06-24 10:19:40 - root - INFO - 日志配置完成 - 目录: logs, 级别: INFO
2025-06-24 10:19:40 - brand_event.api - INFO - [API] Brand Event API initialized successfully
2025-06-24 10:19:40 - brand_event.api - INFO - [API] Brand Event API initialized successfully
2025-06-24 10:20:13 - brand_event.api - INFO - [Session:2tWteCJlDMTzFGaH6hPByW] [API] Processing chat request
2025-06-24 10:20:13 - brand_event.supervisornode - INFO - [Session:2tWteCJlDMTzFGaH6hPByW] [SupervisorNode] Start doing workflow supervision
2025-06-24 10:20:15 - httpx - INFO - HTTP Request: POST https://llm-model-proxy.dev.fc.chj.cloud/agentops/chat/completions "HTTP/1.1 200 OK"
2025-06-24 10:20:15 - brand_event.supervisornode - INFO - [Session:2tWteCJlDMTzFGaH6hPByW] [SupervisorNode] User message analysis: type=task
2025-06-24 10:20:15 - brand_event.supervisornode - INFO - [Session:2tWteCJlDMTzFGaH6hPByW] [SupervisorNode] LLM routing decision: intent_clarification, reason: 用户提出了具体的任务请求，当前工作流状态为INITIALIZING，因此需要进行意图澄清。
2025-06-24 10:20:15 - brand_event.supervisornode - INFO - [Session:2tWteCJlDMTzFGaH6hPByW] [SupervisorNode] Completed workflow supervision, result: route to intent_clarification, reason: 用户提出了具体的任务请求，当前工作流状态为INITIALIZING，因此需要进行意图澄清。
2025-06-24 10:20:15 - agentops_event_sdk_python.subscribers.standard - INFO - Event[8fe3c5b9-2aaf-497c-9338-6f3ea8d68129] sent in 0.07s
2025-06-24 10:20:15 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在分析当前状态和用户消息...
2025-06-24 10:20:15 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在分析当前状态和用户消息...... | session_id: 2tWteCJlDMTzFGaH6hPByW, sandbox_id: 4wLe8tFBqgX2RFmZdWUk8N, task_id: yYaDsDFErnKZOaE3h3y3c
2025-06-24 10:20:15 - agentops_event_sdk_python.subscribers.standard - INFO - Event[c0182484-4048-4b04-8343-31a2b95d3ed2] sent in 0.13s
2025-06-24 10:20:15 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在分析用户消息类型和意图...
2025-06-24 10:20:15 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在分析用户消息类型和意图...... | session_id: 2tWteCJlDMTzFGaH6hPByW, sandbox_id: 4wLe8tFBqgX2RFmZdWUk8N, task_id: yYaDsDFErnKZOaE3h3y3c
2025-06-24 10:20:15 - brand_event.intentclarificationnode - INFO - [Session:2tWteCJlDMTzFGaH6hPByW] [IntentClarificationNode] Start doing intent clarification (round 1)
2025-06-24 10:20:17 - httpx - INFO - HTTP Request: POST https://llm-model-proxy.dev.fc.chj.cloud/agentops/chat/completions "HTTP/1.1 200 OK"
2025-06-24 10:20:17 - brand_event.intentclarificationnode - INFO - [Session:2tWteCJlDMTzFGaH6hPByW] [IntentClarificationNode] Completed intent clarification, result: intent is clear, waiting for approval
2025-06-24 10:20:17 - agentops_event_sdk_python.subscribers.standard - INFO - Event[6ef0d8e0-8588-436b-a094-43429d68ad51] sent in 0.02s
2025-06-24 10:20:17 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.CHAT> 💬 Assistant: 正在分析您的需求，确保完全理解您的需求。...
2025-06-24 10:20:17 - src.messages.event_handler - INFO - 发送Assistant ChatEvent: 正在分析您的需求，确保完全理解您的需求。... | session_id: 2tWteCJlDMTzFGaH6hPByW, sandbox_id: 4wLe8tFBqgX2RFmZdWUk8N, task_id: yYaDsDFErnKZOaE3h3y3c
2025-06-24 10:20:17 - agentops_event_sdk_python.subscribers.standard - INFO - Event[9433fe5d-3ad5-419f-82ab-147c6a524909] sent in 0.13s
2025-06-24 10:20:17 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.LIVE_STATUS> 🔄 Status: 正在分析用户意图...
2025-06-24 10:20:17 - src.messages.event_handler - INFO - 发送LiveStatusEvent: 正在分析用户意图...... | session_id: 2tWteCJlDMTzFGaH6hPByW, sandbox_id: 4wLe8tFBqgX2RFmZdWUk8N, task_id: yYaDsDFErnKZOaE3h3y3c
2025-06-24 10:20:17 - agentops_event_sdk_python.subscribers.standard - INFO - Event[100368f3-1088-4939-b013-7e376e569f62] sent in 0.04s
2025-06-24 10:20:17 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.CHAT> 💬 Assistant: 分析到您的真实需求为：
- 查询事件：理想汽车辅助驾驶讨论热度
- 时间范围：近一个月
- 平台范围：全平台

请确认这个理解是否正确，我将据此为您制定详细的分析计划。...
2025-06-24 10:20:17 - src.messages.event_handler - INFO - 发送Assistant ChatEvent: 分析到您的真实需求为：
- 查询事件：理想汽车辅助驾驶讨论热度
- 时间范围：近一个月
- 平台范围... | session_id: 2tWteCJlDMTzFGaH6hPByW, sandbox_id: 4wLe8tFBqgX2RFmZdWUk8N, task_id: yYaDsDFErnKZOaE3h3y3c
2025-06-24 10:20:17 - agentops_event_sdk_python.subscribers.standard - INFO - Event[cb1a3373-2916-46b2-b7ad-21ce7cba8a58] sent in 0.03s
2025-06-24 10:20:17 - agentops_event_sdk_python.subscribers.standard - INFO - <EventType.STATUS_UPDATE> 📄 Event: AgentStatusEvent
2025-06-24 10:20:17 - src.messages.event_handler - INFO - 发送AgentStatusEvent: idle - 等待用户确认 | session_id: 2tWteCJlDMTzFGaH6hPByW, sandbox_id: 4wLe8tFBqgX2RFmZdWUk8N, task_id: yYaDsDFErnKZOaE3h3y3c
2025-06-24 10:20:17 - brand_event.api - INFO - [Session:2tWteCJlDMTzFGaH6hPByW] [API] Chat request completed successfully
