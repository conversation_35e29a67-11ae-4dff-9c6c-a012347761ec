"""
State definition for brand event analysis workflow.

This module provides state management identical to the existing SpecificState
to ensure complete compatibility with existing Agent implementations.
"""

from typing import Annotated, List, Optional, Dict, Any, Literal
from typing_extensions import TypedDict
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage
from enum import Enum

class WorkflowStatus(str, Enum):
    """Workflow status enumeration - identical to existing implementation."""
    INITIALIZING = "initializing"
    CLARIFYING_INTENT = "clarifying_intent"
    PLANNING = "planning"
    EXECUTING = "executing"
    SUMMARIZING = "summarizing"
    REPORT = "report"
    COMPLETED = "completed"
    FAILED = "failed"

class WorkflowState(TypedDict):
    """
    State for brand event analysis workflow.
    
    This is identical to the existing SpecificState to ensure complete
    compatibility with all existing Agent implementations.
    """
    
    # === Core workflow state ===
    messages: Annotated[List[BaseMessage], add_messages]
    session_id: str
    user_id: Optional[str]
    user_input: str
    
    # === Workflow control ===
    workflow_status: WorkflowStatus
    current_step: str
    requires_human_approval: bool
    
    # === Intent analysis ===
    intent_clarified: bool
    intent_approved: bool
    intent_summary: str
    clarification_result: Optional[Dict[str, Any]]
    clarification_round: Optional[int]
    
    # === Planning ===
    task_plan: Optional[Dict[str, Any]]
    plan_approved: bool
    planning_round: Optional[int]
    
    # === Execution ===
    execution_started: bool
    execution_results: List[Dict[str, Any]]
    execution_report: Optional[str]
    
    # === Summary ===
    summary_report: Optional[str]
    
    # === Reporting ===
    final_report: Optional[str]
    html_report: Optional[str]
    report_dsl_data: Optional[Dict[str, Any]]
    report_dsl_status: Optional[str]
    report_dsl_message: Optional[str]
    summary_data: Optional[Dict[str, Any]]
    upload_result: Optional[Dict[str, Any]]
    
    # === Error handling ===
    error_info: Optional[Dict[str, Any]]
    
    # === Extensions ===
    task_id: Optional[str]
    sandbox_id: Optional[str]
    event_webhook: Optional[str]
    extensions: Optional[Dict[str, Any]]
    metadata: Optional[Dict[str, Any]]
    
    # === Additional fields for compatibility ===
    reason: Optional[str]

def create_initial_state(
    session_id: str,
    user_input: str,
    user_id: Optional[str] = None,
    task_id: Optional[str] = None,
    sandbox_id: Optional[str] = None,
    event_webhook: Optional[str] = None,
    extensions: Optional[Any] = None,
    report_dsl_data: Optional[Dict[str, Any]] = None,
    report_dsl_status: Optional[str] = None,
    report_dsl_message: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    workflow_status: Optional[WorkflowStatus] = None
) -> WorkflowState:
    """
    Create initial state for brand event analysis workflow.
    
    This function is identical to the existing create_initial_state to ensure
    complete compatibility with existing Agent implementations.
    
    Args:
        session_id: Session identifier
        user_input: User input message
        user_id: User identifier
        task_id: Task identifier
        sandbox_id: Sandbox identifier
        event_webhook: Event webhook URL
        extensions: Extension data
        report_dsl_data: Report DSL data structure
        report_dsl_status: Report DSL generation status
        report_dsl_message: Report DSL status message
        metadata: Additional metadata
        workflow_status: Initial workflow status
        
    Returns:
        Initial state for the workflow
    """
    return WorkflowState(
        # Core workflow state
        messages=[],
        session_id=session_id,
        user_id=user_id,
        user_input=user_input,
        
        # Workflow control
        workflow_status=workflow_status or WorkflowStatus.INITIALIZING,
        current_step="start",
        requires_human_approval=False,
        
        # Intent analysis
        intent_clarified=False,
        intent_approved=False,
        clarification_result=None,
        clarification_round=0,
        
        # Planning
        task_plan=None,
        plan_approved=False,
        planning_round=0,
        
        # Execution
        execution_started=False,
        execution_results=[],
        execution_report=None,
        
        # Summary
        summary_report=None,
        
        # Reporting
        final_report=None,
        html_report=None,
        report_dsl_data=report_dsl_data,
        report_dsl_status=report_dsl_status,
        report_dsl_message=report_dsl_message,
        summary_data=None,
        upload_result=None,
        
        # Error handling
        error_info=None,
        
        # Extensions
        task_id=task_id,
        sandbox_id=sandbox_id,
        event_webhook=event_webhook,
        extensions=extensions,
        metadata=metadata,
        
        # Additional fields
        reason=None
    )
