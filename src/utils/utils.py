"""
Utility functions and tools for brand event analysis workflow.

This module combines utilities and tools from existing implementations
to maintain complete compatibility.
"""
import json
import os
import tempfile
import httpx
import logging
import sys
import datetime
from typing import Dict, Any, Optional, List

import jwt
import requests
from langchain_core.messages import BaseMessage, HumanMessage

# ==================== Logging Configuration ====================

def get_logger(name: str) -> logging.Logger:
    """
    Get configured logger instance.
    
    Args:
        name: Logger name
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    
    if not logger.handlers:
        # Configure handler
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    
    return logger

# ==================== Message Processing ====================

def get_latest_user_response(state: Dict[str, Any]) -> str:
    """
    Get the latest user response from state messages.

    
    Args:
        state: Current workflow state
        
    Returns:
        Latest user response
    """
    messages = state.get("messages", [])
    
    # Find the last human message
    for message in reversed(messages):
        if isinstance(message, HumanMessage):
            return message.content
    
    # Fallback to user_input if no human messages found
    return state.get("user_input", "")


def format_plan_message(plan_str: str, is_revision: bool = False) -> str:
    """格式化计划展示消息"""
    prefix = "根据您的反馈，我重新制定了执行计划：" if is_revision else "根据您的需求，我制定了以下执行计划："

    message = f"{prefix}\n\n{plan_str}\n"
    message += "即将开始执行"

    return message


def build_message_history(messages: List[BaseMessage], max_messages: int = 5) -> str:
    """
    Build message history string for prompts.
    
    Args:
        messages: List of messages
        max_messages: Maximum number of messages to include
        
    Returns:
        Formatted message history
    """
    if not messages:
        return "无历史消息"
    
    # Get recent messages
    recent_messages = messages[-max_messages:] if len(messages) > max_messages else messages
    
    history_parts = []
    for i, message in enumerate(recent_messages):
        message_type = "用户" if isinstance(message, HumanMessage) else "助手"
        content = message.content[:200] + "..." if len(message.content) > 200 else message.content
        history_parts.append(f"{i+1}. {message_type}: {content}")
    
    return "\n".join(history_parts)

# ==================== Validation Functions ====================

def validate_input(input_text: str) -> bool:
    """
    Validate user input.
    
    Args:
        input_text: Input text to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not input_text or not isinstance(input_text, str):
        return False
    
    # Check minimum length
    if len(input_text.strip()) < 2:
        return False
    
    # Check for obvious spam or invalid content
    spam_indicators = ["test", "测试", "111", "aaa"]
    text_lower = input_text.lower().strip()
    
    if text_lower in spam_indicators:
        return False
    
    return True

def validate_execution_plan(execution_plan: Dict[str, Any]) -> bool:
    """
    Validate execution plan structure.
    
    Args:
        execution_plan: Execution plan to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not execution_plan or not isinstance(execution_plan, dict):
        return False
    
    # Check required fields
    required_fields = ["title", "steps"]
    for field in required_fields:
        if field not in execution_plan:
            return False
    
    # Check steps structure
    steps = execution_plan.get("steps", [])
    if not isinstance(steps, list) or len(steps) == 0:
        return False
    
    # Validate each step
    for step in steps:
        if not isinstance(step, dict):
            return False
        if "title" not in step:
            return False
    
    return True

# ==================== Stream Writer Type ====================

class StreamWriter:
    """
    Stream writer interface for compatibility.

    This maintains compatibility with existing Agent implementations.
    """

    def __init__(self, writer_func):
        self.writer_func = writer_func

    def __call__(self, data: Dict[str, Any]):
        """Write data to stream."""
        if self.writer_func:
            self.writer_func(data)

# ==================== Authentication Tools ====================
# 配置信息（需与验证函数保持一致）
SECRET_KEY = "brand_event"  # 必须与验证函数相同
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60  # Token有效期（1小时）

def create_access_token(user_id: str) -> str:
    """
    生成JWT Token（与verify_token_and_user配套）

    参数:
        user_id: 用户唯一标识，将作为'sub'存入Token

    返回:
        str: 签名的JWT Token
    """
    # 设置过期时间
    expire = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
        minutes=ACCESS_TOKEN_EXPIRE_MINUTES
    )

    # 构建Payload（必须包含sub字段以匹配验证逻辑）
    to_encode = {
        "sub": user_id,          # 用户ID（subject标准字段）
        "exp": expire,            # 过期时间
        "iat": datetime.datetime.now(datetime.timezone.utc),  # 签发时间
        "type": "access"          # 可选的Token类型标识
    }

    # 生成Token
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# ==================== Report Generation Tools ====================

def upload_html_to_s3(
    html_content: str,
    filename: Optional[str] = None,
    upload_url: str = "https://xuanji-backend-service-dev.inner.chj.cloud/api/v1/ois/upload",
    timeout: int = 300
) -> Dict[str, Any]:
    """
    Upload HTML content to S3 storage.


    Args:
        html_content: HTML content to upload
        filename: File name (optional, auto-generated if not provided)
        upload_url: Upload service URL
        timeout: Request timeout in seconds

    Returns:
        Upload result
    """
    try:
        logger = get_logger("BrandEventTools")
        logger.info("Starting HTML upload to S3")

        # Generate filename if not provided
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d-%H%M%S')
            filename = f"report-{timestamp}.html"

        # Create temporary file
        temp_file_path = os.path.join(tempfile.gettempdir(), filename)

        # Write HTML content to temporary file
        with open(temp_file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        try:
            # Prepare upload file
            with open(temp_file_path, 'rb') as file:
                files = {
                    'file': (filename, file, 'text/html')
                }

                # Send upload request
                response = requests.post(
                    url=upload_url,
                    files=files,
                    timeout=float(timeout)  # 确保 timeout 是数字类型
                )

                # Check response status
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"HTML uploaded successfully: {result}")
                    return {
                        "success": True,
                        "upload_result": result,
                        "filename": filename,
                        "message": "HTML报告上传成功"
                    }
                else:
                    logger.error(f"HTML upload failed: {response.status_code} - {response.text}")
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}",
                        "message": "HTML报告上传失败"
                    }

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except requests.exceptions.Timeout:
        logger = get_logger("BrandEventTools")
        logger.error("HTML upload timeout")
        return {
            "success": False,
            "error": "Request timeout",
            "message": "HTML报告上传超时"
        }
    except requests.exceptions.RequestException as e:
        logger = get_logger("BrandEventTools")
        logger.error(f"Request error: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": f"请求失败：{str(e)}"
        }
    except Exception as e:
        logger = get_logger("BrandEventTools")
        logger.error(f"HTML upload error: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": f"HTML报告上传失败：{str(e)}"
        }

def generate_html_report(
        dsl_data: Dict[str, Any],
        report_service_url: str,
        api_key: str,
        timeout: int = 300
) -> Dict[str, Any]:
    """
    Generate HTML report from DSL data using report service.

    Args:
        dsl_data: Report DSL data structure
        report_service_url: Report service base URL
        api_key: API key for authentication
        timeout: Request timeout in seconds

    Returns:
        Result dict with success status and HTML content or error
    """
    logger = get_logger("BrandEventTools")
    logger.info("Generating HTML report from DSL data")

    try:
        # Build request
        url = f"{report_service_url}/__ui/report"

        # Convert data to JSON string
        logger.info("Report generation dsl_data: %s", dsl_data)
        payload = json.dumps(dsl_data, ensure_ascii=False)
        logger.info("Report generation payload: %s", payload)
        # Build headers with api-key prefix
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

        # Send request
        response = requests.post(
            url,
            headers=headers,
            data=payload,
            timeout= float(timeout) if timeout is not None else None
        )

        # Check response status
        if response.status_code == 200:
            html_content = response.text
            logger.info("Report generated successfully")
            return {
                "success": True,
                "html_content": html_content,
                "content_type": "text/html",
                "message": "报告生成成功"
            }
        else:
            logger.error(f"Report generation failed: {response.status_code} - {response.text}")
            return {
                "success": False,
                "error": f"HTTP {response.status_code}: {response.text}",
                "message": "报告生成失败",
                "status_code": response.status_code
            }

    except requests.exceptions.Timeout:
        logger.error("Report generation timeout")
        return {
            "success": False,
            "error": "Request timeout",
            "message": "报告生成超时"
        }
    except requests.exceptions.RequestException as e:
        logger.error(f"Request error: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": f"请求失败：{str(e)}"
        }
    except Exception as e:
        logger.error(f"Report generation error: {e}")
        logger.error(f"DSL data: {json.dumps(dsl_data, ensure_ascii=False, indent=4)}")
        return {
            "success": False,
            "error": str(e),
            "message": f"报告生成失败：{str(e)}"
        }
async def generate_report_text_summary(summary_data: Dict[str, Any]) -> str:
    """
    Generate structured text summary from parsed DSL data.

    Args:
        summary_data: Parsed summary data from parse_dsl_summary

    Returns:
        Formatted text report
    """

    # AI summary section
    ai_summary = summary_data.get('ai_summary', '')

    text = "已生成HTML可视化图表，请查收。\n\n"
    if ai_summary:
        text += "AI总结\n\n"
        text += f"{ai_summary}\n\n"
    return text

# ==================== DSL Processing Tools ====================
def parse_dsl_summary(report_dsl: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse AI summary and key data from DSL

    Args:
        report_dsl: Report DSL data structure

    Returns:
        Parsed summary data
    """
    try:
        summary_data = {
            "ai_summary": "",
            "key_metrics": [],
            "viewpoints": [],
            "charts_info": []
        }

        for section_key, section in report_dsl.items():
            if not isinstance(section, dict):
                continue

            section_title = section.get("title", "")
            section_description = section.get("description", "")

            # Extract AI summary
            if "AI总结" in section_title or "总结" in section_title:
                summary_data["ai_summary"] = section_description
                logging.info(f"Found AI summary in section: {section_title}")



        return summary_data

    except Exception as e:
        logging.error(f"Error parsing DSL summary: {e}", exc_info=True)
        return {
            "ai_summary": "",
            "key_metrics": [],
            "viewpoints": [],
            "charts_info": []
        }

def create_brand_analysis_dsl() -> Dict[str, Any]:
    """
    Create default brand analysis DSL structure.

    Returns:
        Default brand analysis DSL
    """
    return {
        "title": "品牌舆情分析报告",
        "description": "基于多维度数据的品牌舆情深度分析",
        "sections": {
            "01": {
                "type": "summary",
                "title": "执行摘要",
                "description": "品牌舆情分析核心发现",
                "content": [
                    {
                        "type": "text",
                        "data": "本报告基于全网数据采集和AI智能分析，为品牌提供全面的舆情洞察。通过多维度数据分析，识别品牌传播趋势、用户情感倾向和潜在风险机会。"
                    }
                ]
            },
            "02": {
                "type": "analysis",
                "title": "详细分析",
                "description": "品牌舆情多维度深度分析",
                "content": [
                    {
                        "type": "chart",
                        "chart_type": "line",
                        "title": "舆情趋势分析",
                        "data": {
                            "labels": ["第1周", "第2周", "第3周", "第4周"],
                            "datasets": [
                                {
                                    "label": "正面舆情",
                                    "data": [65, 70, 75, 80],
                                    "borderColor": "#10B981"
                                },
                                {
                                    "label": "负面舆情",
                                    "data": [20, 15, 12, 10],
                                    "borderColor": "#EF4444"
                                }
                            ]
                        }
                    }
                ]
            },
            "03": {
                "type": "section",
                "title": "关键指标",
                "description": "品牌舆情关键性能指标",
                "content": [
                    {
                        "type": "metrics",
                        "data": [
                            {"label": "舆情健康度", "value": "85%", "trend": "up"},
                            {"label": "传播声量", "value": "12,450", "trend": "up"},
                            {"label": "正面占比", "value": "78.5%", "trend": "stable"},
                            {"label": "负面占比", "value": "12.3%", "trend": "down"}
                        ]
                    }
                ]
            }
        }
    }
