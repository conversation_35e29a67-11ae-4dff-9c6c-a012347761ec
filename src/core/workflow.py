"""
Brand event analysis workflow implementation.

This module provides workflow nodes and graph construction
"""
import logging
from typing import Dict, Any, Literal, Optional, List
from functools import wraps

from agentops_event_sdk_python import ChatAttachment
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph
from langgraph.types import Command, StreamWriter
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, SystemMessage
from pydantic import BaseModel, Field
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
import datetime

from .state import WorkflowState, WorkflowStatus
from .config import WorkflowConfiguration
from .prompts import (
    build_supervisor_routing_prompt,
    build_feedback_analysis_prompt,
    build_clarification_prompt,
    PLANNING_SYSTEM_PROMPT,
    build_planning_prompt
)
from src.utils.utils import (
    get_latest_user_response,
    format_plan_message, create_access_token, upload_html_to_s3,
    parse_dsl_summary, generate_report_text_summary, generate_html_report
)
from src.messages.messages import agent_msg, status_msg, feedback_msg
from src.utils.logger import get_workflow_logger, LogMessages


# ==================== Exception Handler Decorator ====================

def node_exception_handler(node_name: str):
    """
    Decorator to handle exceptions in workflow nodes.
    If an exception occurs, it returns a Command to goto __end__ with error status.
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(state: WorkflowState, writer: StreamWriter, config: RunnableConfig) -> Command:
            session_id = state.get('session_id', 'unknown')
            logger = get_workflow_logger(node_name)

            try:
                return await func(state, writer, config)
            except Exception as e:
                logger.error(f"{node_name} failed: {str(e)}", session_id=session_id, exc_info=True)
                writer(status_msg(f"{node_name.lower()}.error"))
                return Command(
                    goto="__end__",
                    update={
                        "workflow_status": WorkflowStatus.FAILED,
                        "error_info": {
                            "message": str(e),
                            "node": node_name.lower()
                        }
                    }
                )

        return wrapper

    return decorator


# ==================== Data Models ====================

class UserMessageType(BaseModel):
    """User message analysis"""
    message_type: Literal[
        "greeting", "task", "supplement", "agreement", "rejection", "detailed_description", "short_reply"] = Field(
        description="Message type"
    )
    user_intent: str = Field(description="User intent analysis")
    extracted_info: str = Field(default="", description="Key information extracted from user message")


class RoutingDecision(BaseModel):
    """Routing decision with analysis"""
    message_analysis: UserMessageType = Field(description="User message analysis result")
    next: Literal["intent_clarification", "planning", "execution", "report", "__end__"] = Field(
        description="Next routing target"
    )
    reason: str = Field(description="Routing decision reason")
    response_message: str = Field(default="", description="Message to return to user (only for greetings)")
    workflow_status: str = Field(description="Updated workflow status")


class UserFeedback(BaseModel):
    """User feedback analysis for clarification or planning results"""
    intent_type: Literal["agreement", "supplement", "rejection"] = Field(description="Intent type")
    next_action: Literal["continue", "retry"] = Field(description="Next action")
    extracted_info: str = Field(default="", description="Extracted supplementary information")


class ClarificationResult(BaseModel):
    """Intent clarification result"""
    intent_clear: bool = Field(description="Whether intent is clear")
    clarification_questions: List[str] = Field(default=[], description="Clarification questions")
    clarification_result: Optional[Dict[str, Any]] = Field(default=None, description="Clarification result")
    summary: str = Field(description="Analysis summary")
    response_message: str = Field(description="Response message")


class PlanStep(BaseModel):
    """Plan step"""
    title: str = Field(description="Step title")
    description: str = Field(description="Step description")
    estimated_time: str = Field(default="", description="Estimated time")
    skip_execute: bool = Field(default=False, description="Whether to skip execution")


class Plan(BaseModel):
    """Execution plan"""
    title: str = Field(description="Plan title")
    objective: str = Field(description="Plan objective")
    steps: List[PlanStep] = Field(description="Execution steps")

    def to_markdown(self) -> str:
        lines = []
        lines.append("")
        lines.append("─" * len(self.title))
        lines.append(f"{self.title}")
        lines.append("─" * len(self.title))
        lines.append("")

        for i, step in enumerate(self.steps, 1):
            lines.append(f"{i}. {step.title}")

            if step.description:
                lines.append(f"   {step.description}")

            lines.append("")

        return "\n".join(lines)


# ==================== Supervisor Node ====================

@node_exception_handler("SupervisorNode")
async def supervisor_node(state: WorkflowState, writer: StreamWriter, config: RunnableConfig) -> Command:
    """
    Supervisor node - Routes workflow based on current state and user intent
    """

    configurable = WorkflowConfiguration.from_runnable_config(config)
    session_id = state.get('session_id', 'unknown')
    logger = get_workflow_logger("SupervisorNode")
    logger.info(LogMessages.WORKFLOW_SUPERVISION_START, session_id=session_id)

    writer(status_msg("supervisor.analyzing_state"))

    writer(status_msg("supervisor.analyzing_message_type"))
    # Build routing prompt and get LLM decision
    prompt = build_supervisor_routing_prompt(state)
    llm = configurable.get_supervisor_llm(temperature=0)
    decision = llm.with_structured_output(RoutingDecision).invoke([HumanMessage(content=prompt)])

    logger.info(
        LogMessages.USER_MESSAGE_ANALYSIS.format(message_type=decision.message_analysis.message_type),
        session_id=session_id)
    logger.info(LogMessages.LLM_ROUTING_DECISION.format(next=decision.next, reason=decision.reason),
                session_id=session_id)

    updates = {
        "reason": decision.reason,
        "workflow_status": decision.workflow_status
    }

    # Handle greeting messages
    if decision.message_analysis.message_type == "greeting" and decision.next == '__end__' and decision.response_message:
        writer(agent_msg("supervisor.greeting_response", message=decision.response_message))
        writer(feedback_msg("supervisor.greeting_help"))

    result = Command(goto=decision.next, update=updates)

    reason = result.update.get('reason', 'N/A')
    logger.info(
        LogMessages.WORKFLOW_SUPERVISION_COMPLETE.format(destination=result.goto, reason=reason),
        session_id=session_id)
    return result


# ==================== Intent Clarification Node ====================

@node_exception_handler("IntentClarificationNode")
async def intent_clarification_node(state: WorkflowState, writer: StreamWriter, config: RunnableConfig) -> Command:
    """
    Intent clarification node - Clarifies user requirements and waits for approval
    """
    configurable = WorkflowConfiguration.from_runnable_config(config)
    logger = get_workflow_logger("IntentClarificationNode")
    session_id = state.get('session_id', 'unknown')
    user_input = state.get("user_input", "")
    round = state.get("clarification_round", 0)

    logger.info(LogMessages.WORKFLOW_INTENT_START.format(round=round + 1), session_id=session_id)

    if round == 0:
        writer(agent_msg("intent.analyzing_requirements"))
        writer(status_msg("intent.analyzing"))

    user_feedback_message = get_latest_user_response(state)
    llm = configurable.get_analyst_llm(temperature=0)

    # Handle multi-round clarification
    if round > 0 and user_feedback_message and not state.get("intent_approved", False):
        writer(status_msg("intent.analyzing_reply"))
        prompt = build_feedback_analysis_prompt(user_feedback_message)
        feedback_analysis = llm.with_structured_output(UserFeedback).invoke([HumanMessage(content=prompt)])
        if feedback_analysis.intent_type == "agreement":
            writer(agent_msg("intent.confirmed"))
            logger.info(LogMessages.WORKFLOW_INTENT_COMPLETE.format(result="user approved requirements"),
                        session_id=session_id)
            return Command(
                goto="planning",
                update={
                    "intent_clarified": True,
                    "intent_approved": True,
                    "workflow_status": WorkflowStatus.PLANNING,
                    "messages": state["messages"] + [
                        HumanMessage(content="感谢您的确认！我现在开始为您制定执行计划。")]
                }
            )

        # Handle supplementary information
        elif feedback_analysis.intent_type == "supplement":
            writer(agent_msg("intent.supplement_understood"))
            logger.info(LogMessages.USER_FEEDBACK_RECEIVED, session_id=session_id)

            supplement = feedback_analysis.extracted_info
            user_input = f"{user_input}\n\n补充信息：{supplement}"

    # Analyze intent clarity
    prompt = build_clarification_prompt(user_input)
    structured_llm = llm.with_structured_output(ClarificationResult, method="json_mode")
    result = structured_llm.invoke([HumanMessage(content=prompt)])

    writer({"agent_message": result.response_message})

    if result.intent_clear:
        # Intent is clear but needs approval
        writer(feedback_msg("feedback.intent.need_approval"))
        logger.info(LogMessages.WORKFLOW_INTENT_COMPLETE.format(result="intent is clear, waiting for approval"),
                    session_id=session_id)

        structured_data = None
        if result.clarification_result:
            structured_data = result.clarification_result

        updates = {
            "intent_clarified": True,
            "intent_approved": False,
            "user_input": user_input,
            "intent_summary": result.summary,
            "clarification_result": structured_data,
            "workflow_status": WorkflowStatus.CLARIFYING_INTENT,
            "messages": state["messages"] + [HumanMessage(content=result.response_message)],
            "clarification_round": round + 1
        }

        return Command(goto="__end__", update=updates)
    else:
        # Need further clarification
        writer(feedback_msg("feedback.intent.need_more_info"))
        logger.info(LogMessages.WORKFLOW_INTENT_COMPLETE.format(result="need further clarification"),
                    session_id=session_id)

        updates = {
            "intent_clarified": False,
            "intent_approved": False,
            "workflow_status": WorkflowStatus.CLARIFYING_INTENT,
            "messages": state["messages"] + [HumanMessage(content=result.response_message)],
            "clarification_round": round + 1
        }

        return Command(goto="__end__", update=updates)


# ==================== Planning Node ====================

@node_exception_handler("PlanningNode")
async def planning_node(state: WorkflowState, writer: StreamWriter, config: RunnableConfig) -> Command:
    """
    Planning node - Creates execution plan and waits for approval
    """
    session_id = state.get('session_id', 'unknown')
    logger = get_workflow_logger("PlanningNode")
    user_input = state.get("user_input")
    clarification_result = state.get("clarification_result")
    intent_summary = state.get("intent_summary")

    previous_plan = state.get("task_plan")
    round = state.get("planning_round", 0)

    logger.info(LogMessages.WORKFLOW_PLANNING_START.format(round=round + 1), session_id=session_id)
    if round == 0:
        writer(agent_msg("planning.creating_plan"))
        writer(status_msg("planning.generating"))

    configurable = WorkflowConfiguration.from_runnable_config(config)
    feedback_messages = get_latest_user_response(state)
    llm = configurable.get_planner_llm(temperature=0)

    # Handle plan feedback
    if round > 0 and feedback_messages and not state.get("plan_approved", False):
        writer(status_msg("planning.analyzing_feedback"))

        prompt = build_feedback_analysis_prompt(feedback_messages)
        feedback_analysis = llm.with_structured_output(UserFeedback).invoke([HumanMessage(content=prompt)])

        # Handle plan approval
        if feedback_analysis.intent_type == "agreement":
            writer(agent_msg("planning.plan_confirmed"))
            logger.info(LogMessages.WORKFLOW_PLANNING_COMPLETE.format(result="user approved plan"),
                        session_id=session_id)
            return Command(
                goto="execution",
                update={
                    "plan_approved": True,
                    "workflow_status": WorkflowStatus.EXECUTING,
                }
            )

        elif feedback_analysis.intent_type == "supplement":
            writer(agent_msg("planning.modification_understood"))
            logger.info(LogMessages.USER_PLAN_MODIFICATION, session_id=session_id)
            supplement = feedback_analysis.extracted_info
            user_input = f"{user_input}\n\n补充信息：{supplement}"

    # Generate execution plan
    writer(status_msg("planning.generating_execution_plan"))
    logger.info(
        LogMessages.WORKFLOW_PLANNING_INPUT.format(
           session_id=session_id,
           user_input=user_input,
           clarification_result=clarification_result,
           intent_summary=intent_summary)

    )
    prompt = build_planning_prompt(user_input, clarification_result, intent_summary, previous_plan)
    plan_messages = [
        SystemMessage(content=PLANNING_SYSTEM_PROMPT),
        HumanMessage(content=prompt)
    ]

    structured_llm = llm.with_structured_output(Plan)
    plan = structured_llm.invoke(plan_messages)

    is_revision = round > 0
    formatted_message = format_plan_message(plan.to_markdown(), is_revision)

    writer(agent_msg("planning.plan_message", message=formatted_message))
    # writer(feedback_msg("feedback.planning.confirm_plan"))

    logger.info(LogMessages.WORKFLOW_PLANNING_COMPLETE.format(result="plan created"),
                session_id=session_id)

    return Command(
        goto="execution",
        update={
            "task_plan": plan.model_dump(),
            "plan_approved": True, # 调整：计划制定后，直接进入执行阶段
            "user_input": user_input,
            "workflow_status": WorkflowStatus.EXECUTING,
            "planning_round": round + 1
        }
    )


# ==================== Execution Node ====================

@node_exception_handler("ExecutionNode")
async def execution_node(state: WorkflowState, writer: StreamWriter, config: RunnableConfig) -> Command:
    """
    Execution node - Executes plan using MCP tools
    """
    session_id = state.get('session_id', 'unknown')
    logger = get_workflow_logger("ExecutionNode")

    plan_dict = state.get("task_plan", {})

    if not plan_dict:
        logger.error(LogMessages.ERROR_NO_EXECUTION_PLAN, session_id=session_id)
        return Command(
            goto="__end__",
            update={
                "error_info": {"message": "未提供执行计划", "node": "execution"},
                "workflow_status": WorkflowStatus.FAILED
            }
        )

    plan = Plan(**plan_dict)

    if not (plan and plan.title and plan.steps and len(plan.steps) > 0):
        logger.error(LogMessages.ERROR_INVALID_EXECUTION_PLAN, session_id=session_id)
        return Command(
            goto="__end__",
            update={
                "error_info": {"message": "执行计划无效", "node": "execution"},
                "workflow_status": WorkflowStatus.FAILED
            }
        )

    total_steps = len(plan.steps)
    logger.info(LogMessages.WORKFLOW_EXECUTION_START.format(total_steps=total_steps), session_id=session_id)

    configurable = WorkflowConfiguration.from_runnable_config(config)

    # Setup MCP tools
    user_id = state.get('user_id') if state.get('user_id') else '<EMAIL>'
    mcp_session_id = state.get('session_id')
    task_id = state.get('task_id')
    sandbox_id = state.get('sandbox_id')

    token = create_access_token(user_id)
    logger.info(LogMessages.JWT_TOKEN_GENERATED.format(user_id=user_id), session_id=session_id)

    server_configs = configurable.create_mcp_server_config(
        user_id=user_id,
        session_id=mcp_session_id,
        task_id=task_id,
        sandbox_id=sandbox_id,
        jwt_token=token
    )

    mcp_client = MultiServerMCPClient(server_configs)
    tools = await mcp_client.get_tools()

    if not tools:
        error_message = "MCP服务未返回任何可用工具"
        logger.error(error_message, session_id=session_id)
        return Command(
            goto="__end__",
            update={
                "error_info": {"message": error_message, "node": "execution"},
                "workflow_status": WorkflowStatus.FAILED
            }
        )

    llm = configurable.get_executor_llm(temperature=0)
    agent = create_react_agent(llm, tools)
    logger.info(LogMessages.MCP_TOOLS_LOADED.format(tool_count=len(tools)), session_id=session_id)

    # Execute plan steps
    results = []
    for idx, step in enumerate(plan.steps):
        if step.skip_execute:
            logger.info(LogMessages.STEP_SKIPPED.format(step_index=idx + 1, step_title=step.title),
                        session_id=session_id)
            continue

        writer(status_msg("execution.executing_step", step_name=step.title))
        logger.info(LogMessages.STEP_START.format(step_index=idx, step_title=step.title),
                    session_id=session_id)

        writer(agent_msg("execution.step_start", step_index=idx + 1, step_title=step.title))

        input = {
            "messages": [
                HumanMessage(
                    content=f"""请执行以下任务：

        任务内容：{step.description}

        调用工具时的要求：
        1. 将上述"任务内容"原样作为query参数传递给MCP工具，不要修改或添加任何内容
        2. query字段内容应该完全等于：{step.description}

        响应要求：
        - 成功接收：回复"已成功接收您的[分析/查询/其他]需求：[简要复述需求内容]，正在为您处理中，请耐心等待..."
        - 提交失败：说明具体原因（权限问题/参数错误/系统繁忙等）并给出建议"""
                )
            ]
        }

        logger.info("=" * 60)
        logger.info(f"🚀 执行步骤请求数据: {step.title}")
        for msg in input["messages"]:
            msg.pretty_print()
        logger.info("=" * 60)

        response = await agent.ainvoke(input)

        logger.info("=" * 60)
        logger.info(f"📥 执行步骤返回数据:")

        if isinstance(response, dict) and "messages" in response:
            for msg in response["messages"]:
                msg.pretty_print()
            result = response["messages"][-1].content
            logger.info("=" * 60)
        else:
            logger.info(f"直接返回: {str(response)[:200]}...")
            logger.info("=" * 60)
            result = str(response)

        # writer({"agent_message", result})
        writer(agent_msg("execution.step_completed", step_index=idx + 1, result=result))
        results.append(result)

    # Generate execution report
    report = f"## {plan.title} - 执行报告\n\n" + "\n\n".join(results)

    writer(agent_msg("execution.all_completed"))
    writer(status_msg("execution.background_processing"))

    logger.info(LogMessages.WORKFLOW_EXECUTION_COMPLETE, session_id=session_id)

    return Command(
        goto="__end__",
        update={
            "workflow_status": WorkflowStatus.REPORT,
            "execution_report": report
        }
    )


# ==================== Report Node ====================

@node_exception_handler("ReportNode")
async def report_node(state: WorkflowState, writer: StreamWriter, config: RunnableConfig) -> Command:
    """
    Report node - Generates and uploads analysis report
    """
    session_id = state.get('session_id', 'unknown')
    logger = get_workflow_logger("ReportNode")

    try:
        logger.info(LogMessages.WORKFLOW_REPORT_START, session_id=session_id)
        writer(agent_msg("report.generating"))

        # Get report DSL data
        dsl_data = state.get("report_dsl_data")
        dsl_status = state.get("report_dsl_status")
        dsl_message = state.get("report_dsl_message")

        # Check DSL status
        if dsl_status == "FAILED":
            error_message = f"报表DSL生成失败：{dsl_message or '未知错误'}"
            logger.error(LogMessages.ERROR_DSL_PARSING_FAILED.format(error=dsl_message or '未知错误'),
                         session_id=session_id)
            writer(agent_msg("report.generation_failed", error=error_message))
            return Command(
                goto="__end__",
                update={
                    "workflow_status": WorkflowStatus.FAILED,
                    "error_info": {"message": error_message, "node": "report"}
                }
            )

        if not dsl_data:
            raise ValueError("Missing report_dsl_data - 需要有效的DSL数据生成报告")

        # Get configuration
        configurable = WorkflowConfiguration.from_runnable_config(config)

        # Step 1: Generate HTML report from DSL
        writer(status_msg("report.calling_service"))
        logger.info(LogMessages.REPORT_SERVICE_CALL, session_id=session_id)

        report_result = generate_html_report(
            dsl_data=dsl_data,
            report_service_url=configurable.report_service_url,
            api_key=configurable.report_api_key,
            timeout=configurable.report_timeout
        )

        if not report_result["success"]:
            error_msg = report_result.get("message", "报告生成失败")
            logger.error(f"Report generation failed: {report_result.get('error', 'Unknown error')}",
                         session_id=session_id)
            raise ValueError(error_msg)

        html_content = report_result["html_content"]

        # Step 2: Upload HTML to S3
        writer(status_msg("report.uploading"))
        logger.info(LogMessages.S3_UPLOAD_START, session_id=session_id)

        filename = f"report-{session_id}-{datetime.datetime.now().strftime('%Y%m%d-%H%M%S')}.html"

        upload_result = upload_html_to_s3(
            html_content=html_content,
            filename=filename,
            upload_url=configurable.upload_service_url,
            timeout=configurable.report_timeout
        )

        if not upload_result["success"]:
            error_msg = upload_result.get("message", "HTML报告上传失败")
            logger.error(LogMessages.S3_UPLOAD_FAILED.format(error=upload_result.get("error", "Unknown error")),
                         session_id=session_id)
            raise ValueError(error_msg)

        logger.info(LogMessages.S3_UPLOAD_SUCCESS.format(result=str(upload_result.get("upload_result", {}))),
                    session_id=session_id)

        # Step 3: Parse summary data and generate text report
        writer(status_msg("report.parsing_summary"))
        summary_data = parse_dsl_summary(dsl_data)

        logger.info(LogMessages.DSL_PARSING.format(ai_summary=summary_data.get('ai_summary', '')))

        # Generate text summary
        final_report = await generate_report_text_summary(summary_data)
        upload_info = upload_result.get("upload_result", {})
        file_name = upload_result.get("filename", "")
        file_key = upload_info.get("fileKey", "")
        writer({"agent_message": final_report})
        writer(
            {"agent_message_with_file":
                {
                    "content": "报告文件",
                    "attachments": [ChatAttachment(
                        file_key=file_key,
                        local_path=file_key,
                        type="file",
                        filename=file_name,
                        content_type="text/html",
                        content_length=len(html_content),
                    )]
                }
            }
        )
        # Complete
        logger.info(LogMessages.WORKFLOW_REPORT_COMPLETE, session_id=session_id)
        writer(feedback_msg("report.other_help"))

        return Command(
            goto="__end__",
            update={
                "workflow_status": WorkflowStatus.INITIALIZING,
                "final_report": final_report,
                # "html_report": html_content,
                # "report_dsl_data": dsl_data,
                "summary_data": summary_data,
                "upload_result": upload_result,
            }
        )

    except Exception as e:
        logger.error(f"Report node error: {str(e)}", session_id=session_id)
        writer(agent_msg("report.generation_failed", error=str(e)))
        return Command(
            goto="__end__",
            update={
                "workflow_status": WorkflowStatus.FAILED,
                "error_info": {"message": str(e), "node": "report"}
            }
        )


# ==================== Workflow Graph Construction ====================

def create_workflow_graph() -> StateGraph:
    """
    Create the brand event analysis workflow graph.

    Returns:
        Compiled workflow graph
    """
    # Create the main workflow graph
    builder = StateGraph(
        WorkflowState,
        config_schema=WorkflowConfiguration
    )

    # Add all workflow nodes
    builder.add_node("supervisor", supervisor_node)
    builder.add_node("intent_clarification", intent_clarification_node)
    builder.add_node("planning", planning_node)
    builder.add_node("execution", execution_node)
    builder.add_node("report", report_node)

    # Set entry point
    builder.set_entry_point("supervisor")

    return builder


# ==================== Workflow Class ====================

class BrandEventWorkflow:
    """
    Brand Event Analysis Workflow class.

    Provides a high-level interface for the brand event analysis workflow,
    maintaining compatibility with existing Agent-based implementations.
    """

    def __init__(self, checkpointer=None):
        """Initialize the workflow with optional checkpointer."""
        if checkpointer is None:
            checkpointer = MemorySaver()

        self.workflow_builder = create_workflow_graph()
        self.workflow = self.workflow_builder.compile(checkpointer=checkpointer)

    def get_graph(self):
        """Get the workflow graph."""
        return self.workflow.get_graph()

    async def ainvoke(self, state: WorkflowState, config=None):
        """Asynchronously invoke the workflow."""
        return await self.workflow.ainvoke(state, config=config)

    async def astream(self, state: WorkflowState, config=None):
        """Asynchronously stream the workflow execution."""
        async for chunk in self.workflow.astream(state, config=config):
            yield chunk

    def invoke(self, state: WorkflowState, config=None):
        """Synchronously invoke the workflow."""
        return self.workflow.invoke(state, config=config)

    def stream(self, state: WorkflowState, config=None):
        """Synchronously stream the workflow execution."""
        for chunk in self.workflow.stream(state, config=config):
            yield chunk


# ==================== Workflow Instance ====================

# Create the compiled workflow graph
workflow_builder = create_workflow_graph()
workflow = workflow_builder.compile(checkpointer=MemorySaver())


# ==================== Utility Functions ====================

async def run_workflow(
        initial_state: WorkflowState,
        config: Optional[Dict[str, Any]] = None,
        writer=None
) -> WorkflowState:
    """
    Run the brand event analysis workflow.

    Args:
        initial_state: Initial workflow state
        config: Optional configuration
        writer: Optional writer function for streaming

    Returns:
        Final workflow state
    """
    try:
        session_id = initial_state.get("session_id", "unknown")
        logging.info(f"Starting workflow execution, session_id: {session_id}")

        # Prepare configuration
        if config is None:
            config = {}

        # Add writer to config if provided
        config["writer"] = writer

        # Create runnable config
        runnable_config = {"configurable": config}

        # Run the workflow
        final_state = await workflow.ainvoke(initial_state, config=runnable_config)

        logging.info(f"Workflow execution completed, session_id: {session_id}")
        return final_state

    except Exception as e:
        logging.error(f"Workflow execution failed: {e}")
        # Return error state
        return {
            **initial_state,
            "workflow_status": WorkflowStatus.FAILED,
            "error_info": {
                "error_type": "workflow_error",
                "message": str(e)
            }
        }


async def stream_workflow(
        initial_state: WorkflowState,
        config: Optional[Dict[str, Any]] = None
):
    """
    Stream the brand event analysis workflow execution.

    Args:
        initial_state: Initial workflow state
        config: Optional configuration

    Yields:
        Workflow state updates
    """
    try:
        session_id = initial_state.get("session_id", "unknown")
        logging.info(f"Starting workflow streaming, session_id: {session_id}")

        # Prepare configuration
        if config is None:
            config = {}

        # Create runnable config
        runnable_config = {"configurable": config}

        # Stream the workflow
        async for chunk in workflow.astream(initial_state, config=runnable_config):
            yield chunk

        logging.info(f"Workflow streaming completed, session_id: {session_id}")

    except Exception as e:
        logging.error(f"Workflow streaming failed: {e}")
        # Yield error state
        yield {
            **initial_state,
            "workflow_status": WorkflowStatus.FAILED,
            "error_info": {
                "error_type": "workflow_streaming_error",
                "message": str(e)
            }
        }


# ==================== Export All Functions ====================

__all__ = [
    "BrandEventWorkflow",
    "supervisor_node",
    "intent_clarification_node",
    "planning_node",
    "execution_node",
    "report_node",
    "workflow",
    "create_workflow_graph",
    "run_workflow",
    "stream_workflow"
]
